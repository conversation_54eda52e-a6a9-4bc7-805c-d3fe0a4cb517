<?php
/**
 * Plugin Name: GPT Translator
 * Description: A plugin for managing translation tasks using OpenAI's GPT.
 * Version: 1.0
 * Author: Oleg Bagmet
 */

if (!defined('ABSPATH')) {
    exit; // Deny direct access
}

require plugin_dir_path(__FILE__) . 'vendor/autoload.php';
require plugin_dir_path(__FILE__) . 'includes/admin-page.php';
require plugin_dir_path(__FILE__) . 'includes/class-translator.php';
require plugin_dir_path(__FILE__) . 'includes/cron.php';
require plugin_dir_path(__FILE__) . 'includes/db.php';
require plugin_dir_path(__FILE__) . 'includes/pref_link_shortcode.php';
require plugin_dir_path(__FILE__) . 'includes/wp-gpt-translator-cli.php';
require plugin_dir_path(__FILE__) . 'includes/posts_meta_fields.php';
 
/*--------------------------------------------------------------
# Admin functions
--------------------------------------------------------------*/
function gpt_translator_admin_enqueue_styles($hook) {
    if ($hook !== 'toplevel_page_translation_queue') {
        return; 
    }
    wp_enqueue_style(
        'bootstrap-css', 
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css', 
        [], 
        '5.3.3'
    );
    wp_enqueue_style(
        'bootstrap-icons',
        'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css',
        [],
        '1.11.3'
    );
    wp_enqueue_script(
        'bootstrap-js', 
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js', 
        [], 
        '5.3.3', 
        true 
    );
    wp_enqueue_style('my-plugin-admin-style', plugin_dir_url(__FILE__) . 'includes/assets/style.css');
    wp_enqueue_script('my-plugin-admin-script', plugin_dir_url(__FILE__) . 'includes/assets/script.js', array(), null, true);
}
add_action('admin_enqueue_scripts', 'gpt_translator_admin_enqueue_styles');


/*--------------------------------------------------------------
# Activation / deactivation
--------------------------------------------------------------*/
function gpt_translator_activate() {
    gpt_translator_createQueueTable();
    gpt_translator_createLogTable();
    gpt_translator_create_chunks_table();
    
    if (!wp_next_scheduled('gpt_translator_watchdog')) {
        wp_schedule_event(time(), 'five_minutes', 'gpt_translator_watchdog');
    }
}
register_activation_hook(__FILE__, 'gpt_translator_activate');


function gpt_translator_deactivate() {
    wp_clear_scheduled_hook('gpt_translator_watchdog');
    gpt_translator_remove_plugin_tables();
}
register_deactivation_hook(__FILE__, 'gpt_translator_deactivate');


function gpt_translator_remove_plugin_tables() {
    global $wpdb;
    $tables = [
        $wpdb->prefix . 'gpt_translator_translation_log',
        //$wpdb->prefix . 'gpt_translator_translation_queue',
        //$wpdb->prefix . 'gpt_translator_translation_chunks',
    ];
    foreach ($tables as $table) {
        $wpdb->query("DROP TABLE IF EXISTS $table");
    }
}



add_action('admin_notices', function () {
    $slugs = get_transient('csv_import_not_found_slugs');
    if (!empty($slugs)) {
        delete_transient('csv_import_not_found_slugs');

        echo '<div class="notice notice-error is-dismissible">';
        echo '<p><strong>Unable to find this posts in database:</strong></p>';
        echo '<ul>';
        foreach ($slugs as $slug) {
            echo '<li>' . esc_html($slug) . '</li>';
        }
        echo '</ul>';
        echo '</div>';
    }
});

?>
