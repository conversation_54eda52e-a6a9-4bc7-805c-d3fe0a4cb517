<?php

if (!defined('ABSPATH')) {
    exit; // Запрет прямого доступа
}



class SimpleOpenAITranslator
{
    private const LOOP_DELAY = 2;
    private const LOOP_MAX_ATTEMPTS = 50;
    private const MAX_RECURSION_DEPTH = 50;
                                    
    private const THREADS_API_URL =  'https://api.openai.com/v1/threads';
    private const MESSAGES_API_URL = 'https://api.openai.com/v1/threads/{thread_id}/messages?run_id={run_id}';
    private const RUNS_API_URL =     'https://api.openai.com/v1/threads/{thread_id}/runs';
    private const RUN_STATUS_URL =   'https://api.openai.com/v1/threads/{thread_id}/runs/{run_id}';

    private ?string $apiKey;
    private ?string $assistantId;

    public array $nonTranslatableWords;
    protected array $nonTranslatableMap = [];
    protected array $reverseNonTranslatableMap = [];

    public ?string $excludePostTypes;
    public ?string $translatableBlockFields;

    private $threadId = null;
    private int $translationCount = 0;

    public $task_id;
    public $overwrite_posts;
    public $parent_task_id;
    public $trans_status;

    public string $site_url;
    public string $toLang;
    public string $fromLang;
    public string $toLocale;
    public string $fromLocale;
    public string $toLangCode;
    public string $fromLangCode;
    public string $toLangName;
    public string $fromLangName;

    public function __construct(string $toLang, string $fromLang)
    {

        global $wp_rewrite; 

        if (empty($wp_rewrite->rules)) {
            $wp_rewrite->init(); // init to be sure url_to_postid() works correctly 
        } 

        $this->site_url = get_site_url();
        $this->apiKey = get_option('gpt_translator_api_key', '');
        $this->assistantId = get_option('gpt_translator_assistant_id', '');

        // Non translatable words
        $this->nonTranslatableWords = array_map('trim', explode(',', get_option('gpt_translator_non_translatable_words', '')));
        
        foreach ($this->nonTranslatableWords as $index => $word) {
            $token = '__EXC_' . $index . '__';
            $this->nonTranslatableMap[$word] = $token;
            $this->reverseNonTranslatableMap[$token] = $word;
        }

        $this->excludePostTypes = get_option('gpt_translator_exclude_post_types', '');

        $this->translatableBlockFields = get_option('gpt_translator_translatable_block_fields', '');
      

       
        if (empty($this->apiKey) || empty($this->assistantId)) {
            print_r("❌ API Key or Assistant ID is not set.\n");
            $this->createLogRecord("❌ API Key or Assistant ID is not set.");
        }


        $toLangData = $this->getLangData($toLang);
        $fromLangData = $this->getLangData($fromLang);

        $this->toLocale = $toLangData['locale']; // DE_de
        $this->fromLocale = $fromLangData['locale'];  // EN_en

        $this->toLangCode = $toLang; // en
        $this->fromLangCode = $fromLang; // de

        $this->toLangName = $toLangData['name']; // Deutsch
        $this->fromLangName = $fromLangData['name']; // English

        //$this->createLogRecord("📡 Starting translations from $this->fromLangName to $this->toLangName");

    }


    public function runPostTranslateSteps(
        $translate_po_file,
        $translate_menus,
        $translate_options,
        $translate_users_description,
        $translate_borlabs_dialog,
        int $parent_task_id
    ): void {

        //$this->createLogRecord("🚀 Starting post-translate actions");

        try {
            if ($translate_po_file) {
                $this->createLogRecord("🚀 Starting PO file translation");
                $this->translatePoFile();
                $this->createLogRecord("🏁 Translating PO file completed!");
            }
            if ($translate_users_description) {
                $this->createLogRecord("🚀 Starting Translate Users Description");
                $this->translateUsersDescription($parent_task_id); 
                $this->createLogRecord("🏁 Translating Users Description completed!");
            }
            if ($translate_menus) {
                $this->createLogRecord("🚀 Starting translate menus");
                $this->createTranslateMenus($parent_task_id); 
                $this->createLogRecord("🏁 Translating Menus completed!");
            }
            if ($translate_options) {
                $this->createLogRecord("🚀 Starting translate theme's banners and options");
                $this->translateOptions($parent_task_id);
                $this->createLogRecord("🏁 Translating theme's banners and options completed!");
            }
            if ($translate_borlabs_dialog) {
                $this->createLogRecord("🚀 Starting translate Borlabs dialog");
                $this->translateBorlabsDialog($parent_task_id); 
                $this->createLogRecord("🏁 Translating Borlabs dialog completed!");
            }
        } catch (\Throwable $e) {
            $this->createLogRecord("🔥 EXCEPTION in doTranslations: " . $e->getMessage());
            //throw $e; 
        }
    }




    public function doTranslations(
        $post_ids, 
        $post_ids_excluded,
        $trans_status, 
        $translate_posts, 
        $overwrite_posts,
        $post_types, 
        int $task_id,
        int $parent_task_id
    ) 
    {
        try {
    
            $this->overwrite_posts = $overwrite_posts; 
            $this->trans_status = $trans_status; 
            $this->task_id = $task_id; 
            $this->parent_task_id = $parent_task_id; 
            

            if ($translate_posts) {
                $this->translateAndCopyPost($post_ids, $post_ids_excluded, $post_types, $task_id);
            }

           
        } catch (\Throwable $e) {
            $this->createLogRecord("🔥 EXCEPTION in doTranslations: " . $e->getMessage());
        }
    }
    
    

    public function translateLargeContent(string $content)
    {
        $dom = new \DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML('<?xml encoding="utf-8" ?>' . $content, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();

        $translatedContent = $this->processNodeRecursively($dom, 0);

        $dom = null;
        gc_collect_cycles();

        return $translatedContent;
    }

    
    public function translateAndCopyPost($post_ids_to_translate, $post_ids_excluded, $post_types, $task_id)
    {   

        //$post_types is a deprecated option for choosing post types to translate for 'Translate all' feature
        global $sitepress, $wpdb;

        try{   
            while ($this->checkTaskExist($task_id)) {

                $post_ids_to_translate = $this->getIdsFromQueue($task_id);
            
                if (empty($post_ids_to_translate) || !is_array($post_ids_to_translate)) {
                    //$this->createLogRecord("✅ Translation completed");
                    break;
                }
                
                // take first id from array
                $post_id = array_shift($post_ids_to_translate);

           
                $this->createLogRecord("🚀 Starting post id: {$post_id} translation");


                do_action('wpml_switch_language', $this->fromLangCode);
                sleep(3);



                $original_post = get_post($post_id);

                if (!$original_post) {
                   
                    $this->createLogRecord("⚠️ Post not found\n");
                    $this->removeIdFromQueue($this->task_id, $post_id, $post_id, 'gpt_translator_translation_chunks');
                    $this->removeIdFromQueue($this->parent_task_id, $post_id, $post_id, 'gpt_translator_translation_queue');
                    continue;
                }

                if (is_wp_error($original_post)) {
                    $this->createLogRecord("⚠️ WP_Error getting post: " . $original_post->get_error_message());
                    continue;
                }

                $original_post_type = $original_post->post_type;

               


                $translated_id = apply_filters(
                    'wpml_object_id',
                    $post_id,                        
                    $original_post->post_type,       
                    false,                            
                    $this->toLangCode                
                );

               
            

                if ($translated_id) {
                    if (!$this->overwrite_posts) { 
                        print_r("ℹ️ Post have translation already, skip translation \n");
                        $this->createLogRecord("ℹ️ Post have translation already, skip translation");
                        $this->removeIdFromQueue($this->task_id, $post_id, $post_id, 'gpt_translator_translation_chunks');
                        $this->removeIdFromQueue($this->parent_task_id, $post_id, $post_id, 'gpt_translator_translation_queue');
                        continue;
                    } 
                }
                
                $translated_title =         $this->translate($original_post->post_title);
                $translated_post_excerpt =  $this->translate($original_post->post_excerpt);
                //$translated_content = '';
                $translated_content =       $this->translateLargeContent($original_post->post_content);
                $translated_content =       $this->normalize_content($translated_content);

 
            
               
                if($original_post_type === 'post'){
                    $translated_name = $this->transliterate_title($translated_title);
                } 
                else{
                    $translated_original_name = $this->translate($original_post->post_name);
                    $translated_name =          $this->transliterate_title($translated_original_name);
                }

               
                $new_post_data = [
                    'post_title'         => $translated_title,
                    'post_name'          => $translated_name,
                    'post_content'       => $translated_content,
                    'post_type'          => $original_post_type,
                    'post_excerpt'       => $translated_post_excerpt,
                    'post_author'        => $original_post->post_author,
                    'post_parent'        => $original_post->post_parent,
                    'post_password'      => $original_post->post_password,
                    'comment_status'     => $original_post->comment_status,
                    'ping_status'        => $original_post->ping_status,
                    'post_date'          => $original_post->post_date,
                    'post_date_gmt'      => $original_post->post_date_gmt,
                    'post_status'        => $this->trans_status,
                ];
               

                if (
                    $translated_id &&
                    (
                        ($this->trans_status === 'publish') ||
                        ($original_post->post_status === 'draft' && $this->trans_status === 'draft')
                    )
                ) {
                                           
                  
                    $this->createLogRecord("ℹ️ Updating new translation to existing post");

                    $new_post_data['ID'] = $translated_id;

                    try {   

                        do_action('wpml_switch_language', $this->toLangCode);
                        sleep(3);

                        $new_post_id = wp_update_post($new_post_data);

                        sleep(3);

                        $thumbnail_id = get_post_thumbnail_id($post_id);

                        
                        if ($thumbnail_id) {
                            set_post_thumbnail($new_post_id, $thumbnail_id);
                        }

                        
        
                    } catch (\Throwable $e) {
                        $errorMsg = is_wp_error($e) ? $e->get_error_message() : $e->getMessage();
                        $this->createLogRecord("❌ Error: " . $errorMsg);

                        $this->removeIdFromQueue($this->task_id, $post_id, $new_post_id, 'gpt_translator_translation_chunks');
                        $this->removeIdFromQueue($this->parent_task_id, $post_id, $new_post_id, 'gpt_translator_translation_queue');
                        continue;
                    }



                    $this->translatePostMetaValues($post_id, $translated_id);


                    $this->removeIdFromQueue($this->task_id, $post_id, $new_post_id, 'gpt_translator_translation_chunks');
                    $this->removeIdFromQueue($this->parent_task_id, $post_id, $new_post_id, 'gpt_translator_translation_queue');




                    $new_post_link = $this->getTranslationUrl($original_post_type, $new_post_id);
                    
                    $new_post_url = '<a href="' . esc_url($new_post_link) . '" target="_blank">' . esc_html($new_post_link) . '</a>';

                    print_r("💾 Post updated: $new_post_link \n");
                    $this->createLogRecord("💾 Post updated: " . $new_post_url);  
             
                } 
                else { // create new post
                    try {

                        if ($original_post->post_type === 'post') {
                            $translated_post_cats = $this->getToLangCategories($post_id);
                        
                            if (empty($translated_post_cats)) {
                                $this->createLogRecord("⚠️ No translated categories found");
                            }
                            else{
                                $new_post_data['post_category'] = $translated_post_cats;
                            }
                        }

                        $new_post_data['post_status'] = $this->trans_status;
                        
                        

                        do_action('wpml_switch_language', $this->toLangCode);
                        sleep(3);
                        
                        $new_post_id = wp_insert_post($new_post_data, true);

                        sleep(3);

                        $thumbnail_id = get_post_thumbnail_id($post_id);
                        if ($thumbnail_id) {
                            set_post_thumbnail($new_post_id, $thumbnail_id);
                            $this->createLogRecord("🖼 Featured image set for created post ID: {$new_post_id}");
                        }

                        
        
                    } catch (\Throwable $e) {
                        $this->createLogRecord("❌ Post creation error: " . $e->getMessage());
                        $this->removeIdFromQueue($this->task_id, $post_id, $new_post_id, 'gpt_translator_translation_chunks');
                        $this->removeIdFromQueue($this->parent_task_id, $post_id, $new_post_id, 'gpt_translator_translation_queue');
                        continue;
                    }

                    sleep(2);
                    $this->translatePostMetaValues($post_id, $new_post_id);
                    sleep(5);

                    $element_type = 'post_' . $original_post_type;
                    $trid = $this->getWPMLtrid($post_id, $element_type);

        
                    $set_language_args = array(
                        'element_id'    => $new_post_id,
                        'element_type'  => $element_type,
                        'trid'   => $trid,
                        'language_code'   => $this->toLangCode,
                        'source_language_code' => $this->fromLangCode
                    );
                    
                    do_action( 'wpml_set_element_language_details', $set_language_args );


                    sleep(5);
        
        
                    if ($original_post_type === 'page') {
                        $original_template = get_post_meta($post_id, '_wp_page_template', true);
                        if ($original_template) {
                            update_post_meta($new_post_id, '_wp_page_template', $original_template);
                            sleep(5);
                        }
                    }

                
                    $this->removeIdFromQueue($this->task_id, $post_id, $new_post_id, 'gpt_translator_translation_chunks');
                    $this->removeIdFromQueue($this->parent_task_id, $post_id, $new_post_id, 'gpt_translator_translation_queue');

                    
                    $new_post_link = $this->getTranslationUrl($original_post_type, $new_post_id);
                    $new_post_url = '<a href="' . esc_url($new_post_link) . '" target="_blank">' . esc_html($new_post_link) . '</a>';

                    $this->createLogRecord("💾 Post created: " . $new_post_url);

                }

                update_post_meta($new_post_id, 'source_post_ID', $post_id);
                update_post_meta($new_post_id, 'from_lang', $this->$fromLang);
                update_post_meta($new_post_id, 'to_lang', $this->$toLang);

                gc_collect_cycles();
        
            }
        }
        catch (\Exception $e) {
            print_r("❌ Exception: " . $e->getMessage() . "\n");
            $this->createLogRecord("❌ Exception: " . $e->getMessage());
        } catch (\Error $e) {
            print_r("❌ Error: " . $e->getMessage() . "\n");
            $this->createLogRecord("❌ Error: " . $e->getMessage());
        }
    }
  



    private function getTranslationUrl($original_post_type, $new_post_id){

        $base_url = "{$this->site_url}/{$this->toLangCode}/";
        $query = ($original_post_type === "page") 
            ? "{$original_post_type}_id={$new_post_id}" 
            : "p={$new_post_id}";

        if ($this->trans_status === 'draft') {
            $query .= "&preview=true";
        }

        return $base_url . '?' . $query;
    }



   /// Blocks and html proccessing


    private function processNodeRecursively(\DOMNode $node)
    {
        $translatedParts = [];
        
        foreach ($node->childNodes as $childNode) {           

            if ($childNode->nodeType === XML_ELEMENT_NODE) {
              
                $translatedParts[] = $this->processHtmlNode($childNode);

            } elseif ($childNode->nodeType === XML_TEXT_NODE) {
               
                $translatedParts[] = $this->processTextNode($childNode);

            } elseif ($childNode->nodeType === XML_COMMENT_NODE) {
              
                $translatedParts[] = $this->processCommentNode($childNode);

            }
            unset($childNode);
        }
        gc_collect_cycles();
        return implode('', $translatedParts);
    }

    private function processHtmlNode(\DOMNode $node)
    {        

        $translatableAttrs = ['placeholder', 'value', 'title', 'aria-label'];
        foreach ($translatableAttrs as $attrName) {
            if ($node->hasAttribute($attrName)) {
                $original = $node->getAttribute($attrName);
                if (trim($original) !== '') {
                    $translated = $this->translate($original);
                    $node->setAttribute($attrName, $translated);
                }
            }
        }
    
        $hasSpecialChild = false;
    
        foreach ($node->childNodes as $childNode) {
            if (
                $childNode->nodeType === XML_ELEMENT_NODE &&
                in_array($childNode->nodeName, [
                    'a', 'em', 'strong', 'b', 'i', 'u', 's', 'sub', 'sup', 'mark', 'br', 
                    'span', 'small', 'big', 'del', 'ins', 'abbr', 'cite', 'q', 'code', 'img'
                ], true)
            ) {
                $hasSpecialChild = true;
                break;
            }
        }
    
        if ($hasSpecialChild) {
            $content = $this->serializeNode($node); 
            $decodedContent = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');
            $decodedContent = str_replace(
                ['%5B', '%5D', '%20'], // decode broken shortcode symbols  <a href="#%5Bpref-link%20id=12317%20pref-lang=de%5D">
                ['[', ']', ' '],
                $decodedContent
            );
            $translatedContent = $this->translate($decodedContent); 
            return $translatedContent; 
        }
    
        $openingTag = $this->getOpeningTag($node);
        $content = $this->processNodeRecursively($node);
        $closingTag = "</{$node->nodeName}>";
    
        return $openingTag . $content . $closingTag;
    }

    private function processTextNode(\DOMNode $node)
    {
        $textContent = trim($node->textContent);        
        if(!($textContent === '' || ctype_space($textContent))){
            try {
                
                $textToTranslate = html_entity_decode($textContent, ENT_QUOTES | ENT_HTML5, 'UTF-8');
                $translatedText = $this->translate($textToTranslate);
                return $translatedText;

            } catch (\Throwable $e) {
                $this->createLogRecord("❌ Translation failed for text: " . $textContent);
                return $textContent;
            }
        }
        return '';
    }

    private function getOuterHTML(\DOMNode $node): string
    {
        $dom = $node->ownerDocument;
        return $dom->saveHTML($node);
    }
    
    private function processCommentNode(\DOMNode $node)
    {
        $originalComment = $this->getOuterHTML($node);
        $hasTrailingSlash = str_ends_with(trim($originalComment), '/-->');
    
        $commentContent = $node->nodeValue;
    
        if (preg_match('/^\/wp:/', ltrim($commentContent))) {
            return "<!--{$commentContent}-->";
        }
    
        if (trim($commentContent) === 'wp:html') {
            $html = '';
            $nextNode = $node->nextSibling;
    
            // get all nodes till <!-- /wp:html -->
            while ($nextNode && !($nextNode->nodeType === XML_COMMENT_NODE && trim($nextNode->nodeValue) === '/wp:html')) {
                $html .= $node->ownerDocument->saveHTML($nextNode);
                $next = $nextNode->nextSibling;
                $node->parentNode->removeChild($nextNode);
                $nextNode = $next;
            }
    
            $translated = $this->translateLargeContent($html);
            return "<!-- wp:html -->" . $translated;
        }
    
        // Get prefix
        preg_match('/^\s*([^\{]+)\{/', $commentContent, $matches);
        $prefix = isset($matches[1]) ? trim($matches[1]) : '';
    
        // delete prefixes, keep only JSON
        $jsonContent = preg_replace('/^\s*[^\{]+\{/', '{', $commentContent);
        $jsonContent = preg_replace('/\}[^}]*$/', '}', $jsonContent);
        $decodedJson = json_decode($jsonContent, true);
    
        if (json_last_error() !== JSON_ERROR_NONE) {
            //$this->createLogRecord("❌ JSON decode {$commentContent} error: " . json_last_error_msg());
            return "<!-- {$commentContent} -->";
        }
    
        if ($prefix === 'wp:navigation') {
            if (isset($decodedJson['ref'])) {
                $refId = $decodedJson['ref'];
    
                $navigationPost = get_post($refId);
    
                if ($navigationPost) {
                    $translatedNavigation = $this->translateNavigationContent($navigationPost->post_content);
    
                    $nav_post = array(
                        'post_title' => 'Translated Menu',
                        'post_content' => $translatedNavigation,
                        'post_status' => 'publish',
                        'post_type' => 'wp_navigation',
                    );
    
                    $menu_id = wp_insert_post($nav_post);
    
                    if (is_wp_error($menu_id)) {
                        $this->createLogRecord("❌ Error inserting wp_navigation: " . $menu_id->get_error_message());
                        return $this->returnComment($prefix, $decodedJson, $hasTrailingSlash);
                    } elseif ($menu_id === 0) {
                        $this->createLogRecord("❌ wp_insert_post returned 0");
                        return $this->returnComment($prefix, $decodedJson, $hasTrailingSlash);
                    } else {
                        //$this->createLogRecord("💾 wp_navigation post created with ID: " . $menu_id);
                    }
    
                    $decodedJson['ref'] = $menu_id;
                    return $this->returnComment($prefix, $decodedJson, $hasTrailingSlash);
                } else {
                    print_r("❌ No post found with ID {$refId}\n");
                    $this->createLogRecord("❌ No post found with ID {$refId}");
                }
            }
        }
    
        if ($prefix === 'wp:embed') {
            $embedContent = "<!-- {$commentContent} -->";
            $nextNode = $node->nextSibling;
    
            while ($nextNode) {
                $embedContent .= $node->ownerDocument->saveHTML($nextNode);
                $next = $nextNode->nextSibling;
                $node->parentNode->removeChild($nextNode);
    
                if (
                    $nextNode->nodeType === XML_COMMENT_NODE &&
                    trim($nextNode->nodeValue) === '/wp:embed'
                ) {
                    break;
                }
    
                $nextNode = $next;
            }
    
            return $embedContent;
        }
    
        
        if ($prefix === 'wp:theme/card-posts') {
        
            if (isset($decodedJson['postsData']['data']) && is_array($decodedJson['postsData']['data'])) {
                $translatedPosts = [];
        
                // Получаем список полей, которые нужно переводить
                $translatableFields = array_map('trim', explode(',', $this->translatableBlockFields));
        
                foreach ($decodedJson['postsData']['data'] as $postData) {
                    $originalId = $postData['id'] ?? null;
        
                    do_action('wpml_switch_language', $this->toLangCode);
                    $translatedId = $originalId ? apply_filters('wpml_object_id', $originalId, 'post', false, $this->toLangCode) : null;
                    if (!$translatedId && $originalId) {
                        $translatedId = $originalId;
                    }
        
                    $translatedPost = ['id' => $translatedId];
        
                    foreach ($postData as $key => $value) {
                        if (in_array($key, $translatableFields, true) && is_string($value)) {
                            $translatedPost[$key] = $this->translate($value);
                        } else {
                            $translatedPost[$key] = $value;
                        }
                    }
        
                    $translatedPosts[] = $translatedPost;
                }
        
                $decodedJson['postsData']['data'] = $translatedPosts;
            }
        
            return $this->returnComment($prefix, $decodedJson, $hasTrailingSlash);
        }
        
        
        
        
           


        $translatableBlockFields = array_map('trim', explode(',', $this->translatableBlockFields));

        $translatedJson = $this->translateNestedJsonWithValidation($decodedJson, $translatableBlockFields);

    
        // Возврат обновленного комментария
        return $this->returnComment($prefix, $translatedJson, $hasTrailingSlash);
    }
    
    
    private function returnComment(string $prefix, $content, bool $hasTrailingSlash): string
    {
   
        if (is_array($content)) {
            $content = json_encode($content, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        }

        return "<!-- {$prefix} {$content}" . ($hasTrailingSlash ? ' /-->' : ' -->');
    }
        



    private function translateNestedJsonWithValidation($data, $keysToTranslate)
    {
        if (!is_array($keysToTranslate)) {
            $keysToTranslate = explode(",", $keysToTranslate);
        }
    
        if (is_array($data)) {
            foreach ($data as $key => $value) {
                if (in_array($key, $keysToTranslate, true)) {
                    $data[$key] = $this->translateComplexValue($value, $keysToTranslate);
                } elseif (is_array($value)) {
                    $data[$key] = $this->translateNestedJsonWithValidation($value, $keysToTranslate);
                }
            }
        }
    
        return $data;
    }
    
    private function translateComplexValue($value, $keysToTranslate)
    {

       
        $excludedKeys = ['icon', 'type', 'fontAwesomeIcon', 'prefix', 'iconName', 'iconObj', 'className', 'gbIdentifier', 'gb_media_desktop', 'style', 'name', 'elements', 'tag', 'attrs'];
    
        if (is_string($value)) {
            return $this->isTranslatable($value)
                ? $this->translate($value)
                : $value;
        }
    
        if (is_array($value)) {
            foreach ($value as $k => $v) {
        
                if (in_array($k, $excludedKeys, true)) {
                    continue; 
                }
    
                if (is_string($v)) {
                    if ($this->isTranslatable($v)) {
                        $value[$k] = $this->translate($v);
                    }
                } elseif (is_array($v)) {
                    // Переводим props.children[]
                    if (isset($v['props']['children'])) {
                        if (is_array($v['props']['children'])) {
                            $v['props']['children'] = array_map(function ($child) {
                                return is_string($child) && $this->isTranslatable($child)
                                    ? $this->translate($child)
                                    : $child;
                            }, $v['props']['children']);
                        } elseif (is_string($v['props']['children']) && $this->isTranslatable($v['props']['children'])) {
                            $v['props']['children'] = $this->translate($v['props']['children']);
                        }
                        $value[$k] = $v;
                    }
    
                    // Переводим props.href, если нужно
                    if (isset($v['props']['href']) && is_string($v['props']['href']) && in_array('href', $keysToTranslate, true)) {
                        $v['props']['href'] = $this->translate($v['props']['href']);
                        $value[$k] = $v;
                    } else {
                        $value[$k] = $this->translateComplexValue($v, $keysToTranslate);
                    }
                }
            }
        }
    
        return $value;
    }
    
    
    private function isTranslatable($value)
    {
        $trimmed = trim($value, '"');
        return !empty($trimmed)
            && $value !== '""'
            && !ctype_digit($trimmed)
            && !preg_match('/^[^\p{L}]+$/u', $trimmed); // Без букв — отфильтрует URL!
    }
    
    
    
    
    


    

    private function serializeNode(\DOMNode $node): string
    {
        $dom = new \DOMDocument();
        $clonedNode = $dom->importNode($node, true);
        $dom->appendChild($clonedNode);
        $html = $dom->saveHTML();
        $dom = null;
        gc_collect_cycles();
        return $html;
    }

    private function getOpeningTag(\DOMNode $node): string
    {
        $attributes = [];
        foreach ($node->attributes as $attr) {
            $attributes[] = "{$attr->nodeName}=\"" . htmlspecialchars($attr->nodeValue) . "\"";
        }

        $attributesString = $attributes ? ' ' . implode(' ', $attributes) : '';
        return "<{$node->nodeName}{$attributesString}>";
    }
        









    // Tasks

    private function getIdsFromQueue($task_id) {
        global $wpdb;
        $table_name = $wpdb->prefix . 'gpt_translator_translation_chunks';
        $ids_json = $wpdb->get_var($wpdb->prepare("SELECT ids_to_translate FROM $table_name WHERE id = %d", $task_id));
        $post_ids_to_translate = json_decode($ids_json, true);

    
        // Проверяем, если массив содержит только пустую строку, то обнуляем его
        if (is_array($post_ids_to_translate) && count($post_ids_to_translate) === 1 && $post_ids_to_translate[0] === '') {
            array_splice($post_ids_to_translate, 0);
        }
    
        // Если это не массив, делаем его пустым
        if (!is_array($post_ids_to_translate)) {
            $post_ids_to_translate = array();
        }
    
        return $post_ids_to_translate;
    }
    
    private function checkTaskExist($task_id) {
        global $wpdb;
    
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM {$wpdb->prefix}gpt_translator_translation_chunks WHERE id = %d",
            $task_id
        ));
    
        if ($count === null) {
            $error = 'DB error: ' . $wpdb->last_error;
            $this->createLogRecord($error);
            print_r($error . "\n");
            return false;
        }
    
        if ((int) $count < 1) {
            print_r("❌ Task was deleted, translation stopped.\n");
            //delete_transient('gpt_translator_lock');
            $this->createLogRecord("❌ Task was deleted, translation stopped.");
            return false;
        }
    
        return true;
    }
    
    private function updateQueueTime($task_id, $parent_task_id) {
        global $wpdb;
    
        $table_parent = $wpdb->prefix . 'gpt_translator_translation_queue';
        $table_chunk  = $wpdb->prefix . 'gpt_translator_translation_chunks';
        $current_time = current_time('mysql');
    
        $results = [];
    
        // Обновляем родительскую задачу
        $updated_parent = $wpdb->update(
            $table_parent,
            ['last_update' => $current_time],
            ['id' => $parent_task_id]
        );
        if ($wpdb->last_error) {
            $error = "DB error updating parent task ($parent_task_id): " . $wpdb->last_error;
            $this->createLogRecord($error);
            print_r($error . "\n");
        }
        $results['parent_updated'] = $updated_parent !== false;
    
        // Обновляем текущий чанк
        $updated_chunk = $wpdb->update(
            $table_chunk,
            ['last_update' => $current_time],
            ['id' => $task_id]
        );
        if ($wpdb->last_error) {
            $error = "DB error updating chunk task ($task_id): " . $wpdb->last_error;
            $this->createLogRecord($error);
            print_r($error . "\n");
        }
        $results['chunk_updated'] = $updated_chunk !== false;
    
        return $results;
    }
    

    private function removeIdFromQueue($task_id, $id_to_remove, $new_post_id, $table) {
        global $wpdb;
        $table_name = $wpdb->prefix . $table;
    
        // Получаем текущие значения ids_to_translate
        $ids_json = $wpdb->get_var($wpdb->prepare(
            "SELECT ids_to_translate FROM $table_name WHERE id = %d", 
            $task_id
        ));
        $ids_array = json_decode($ids_json, true);
    
        if (!is_array($ids_array)) {
            return false;
        }
    
        $key = array_search($id_to_remove, $ids_array);
        if ($key === false) {
            return false; // ID не найден — ничего не делаем
        }
    
        // Удаляем ID из массива
        unset($ids_array[$key]);
        $ids_array = array_values($ids_array); // переиндексация
    
        // Получаем уже переведённые ID
        $translated_json = $wpdb->get_var($wpdb->prepare(
            "SELECT translated_posts_ids FROM $table_name WHERE id = %d", 
            $task_id
        ));
        $translated_array = json_decode($translated_json, true);
        if (!is_array($translated_array)) {
            $translated_array = [];
        }
    
        // Добавляем ID в список переведённых, если его там ещё нет
        if (!in_array($new_post_id, $translated_array)) {
            $translated_array[] = $new_post_id;
        }
    
        // Обновляем обе ячейки
        $updated = $wpdb->update(
            $table_name,
            [
                'ids_to_translate'      => json_encode($ids_array),
                'translated_posts_ids'  => json_encode(array_values($translated_array))
            ],
            ['id' => $task_id]
        );
    
        return ($updated !== false);
    }
    
    





    // Logging

    public static function createLogRecord($message){
        global $wpdb;
        $table_name = $wpdb->prefix . 'gpt_translator_translation_log';
        $wpdb->insert(
            $table_name,
            array(
                'log_time' => current_time('mysql'),
                'message'  => $message
            ),
            array(
                '%s',
                '%s'
            )
        );
    }









    

    

























    // Features

    private function translateNavigationContent(string $content)
    {
        $pattern = '/<!-- wp:navigation-link\s+({.*?})\s*\/-->/';

        $toLanguage = $this->toLangName;
        $fromLanguage = $this->fromLangName;
    
        $callback = function ($matches) use ($toLanguage, $fromLanguage) {
            $json = $matches[1];
            $data = json_decode($json, true);
    
            if (json_last_error() !== JSON_ERROR_NONE) {
                return $matches[0];
            }
    
            if (isset($data['label'])) {
                $data['label'] = $this->translate($data['label']);
            }
    
            $translatedJson = json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
            return "<!-- wp:navigation-link {$translatedJson} /-->";
        };

        return preg_replace_callback($pattern, $callback, $content);
    }





   // Internal Links features

    private function resolveRedirection(?string $path): ?string {

        global $wpdb;

        if ($path === null) {
            print_r("❌ resolveRedirection called with null path\n");
            return null;
        }

        // Удаляем domain и схему, оставляем только путь
        $relative_path = '/' . ltrim($path, '/');

        // Ищем в таблице redirection_items путь, на который настроен редирект
        $redirect_target = $wpdb->get_var( $wpdb->prepare("
            SELECT action_data 
            FROM {$wpdb->prefix}redirection_items
            WHERE url = %s AND status = 'enabled'
            LIMIT 1
        ", $relative_path) );

        if ($redirect_target) {
            $action = maybe_unserialize($redirect_target);
            if (is_string($action)) {
                return $action;
            } elseif (is_array($action) && isset($action['url'])) {
                return $action['url'];
            }
        }

        return null;
    }

    private function replaceInternalLinksWithShortcode($text)
    {
        // Если весь текст — это только ссылка (без HTML), проверим отдельно
        if (preg_match('/^(https?:\/\/|www\.)[^\s]+$/i', trim($text))) {
            return $this->convertRawHrefToShortcode(trim($text)) ?? $text;
        }
    
        return preg_replace_callback(
            '/<a\s+([^>]*)href=["\']([^"\']+)["\']([^>]*)>(.*?)<\/a>/is',
            function ($matches) {
                $href = $matches[2];
    
                if (preg_match('/^\#?\[pref-link/', $href)) {
                    // Заменяем значение pref-lang
                    $href = preg_replace_callback(
                        '/(pref-lang=)([^\]\s]+)/',
                        function ($m) {
                            return $m[1] . $this->toLangCode;
                        },
                        $href
                    );
                    return '<a ' . $matches[1] . 'href="' . $href . '"' . $matches[3] . '>' . $matches[4] . '</a>';
                }
    
                return $this->replaceLink($matches[0]);
            },
            $text
        );
    }
    

    private function getPostIdFromUrl($path)
    {
        $redirected = $this->resolveRedirection($path);
            if ($redirected) {
                $path = $redirected;
            }

            // Удаляем префикс языка "/en/" 
            $pattern = '#^/' . preg_quote($this->fromLangCode, '#') . '/#';
            $path = preg_replace($pattern, '', $path); // сначала удаляем префикс языка
            $path = trim($path, '/');                  // потом обрезаем хвостовой/начальный слэш
            $slug = basename($path);
            // SQL-запрос, чтобы найти пост с таким slug
            global $wpdb;

            $post = $wpdb->get_row( $wpdb->prepare("
                SELECT ID, post_type FROM $wpdb->posts
                WHERE (post_name = %s OR post_name LIKE %s)
                AND post_status = 'publish'
                LIMIT 1
            ", $slug, '%' . $slug . '%') );

            return $post;
        
    }
    
    private function convertRawHrefToShortcode(string $href): ?string
    {
      
        //print_r("Input url: " . $href . "\n");

        if (strpos($href, '[pref-link') === 0 || strpos($href, '#[pref-link') === 0) {
            // Пропускаем или не переводим
            //print_r("Skipping shorcode: " . $href . "\n");
            
            return $href; // fallback — external or unknown internal
        }

        $post_id = null;
        $parsed_url = parse_url($href);
        $is_relative = !isset($parsed_url['scheme']) && !isset($parsed_url['host']);
        $host = $parsed_url['host'] ?? '';
        $normalized_host = preg_replace('/^www\./i', '', $host);

        $allowed_domains = [
            'sellerlogic-dev.sl.local',
            'sellerlogic-stage.sl.local',
            'sellerlogic-rc.sl.local',
            'sellerlogic.com',
            'localhost'
        ];

        $is_allowed_domain = in_array($normalized_host, $allowed_domains, true);
    
    
        if ($is_relative || $is_allowed_domain) {
           

            $site_url = rtrim($this->site_url, '/');
            $path = isset($parsed_url['path']) ? $parsed_url['path'] : '';
            $normalized_url = $site_url . $path;

            // Получаем путь из URL (например: /en/some-page/)
            $path = wp_parse_url($normalized_url, PHP_URL_PATH);


            $post = $this->getPostIdFromUrl($path);

            
            // Если нашли пост
            if ( $post ) {
                $post_id = $post->ID;
                return '#[pref-link id=' . intval($post_id) . ' pref-lang=' . $this->toLangCode . ']';
                
            } else {
                $post_id = 0; 
            }
    
            // Try author
            if (preg_match('#/author/([^/]+)/?#', $path, $matches)) {
                $user = get_user_by('slug', $matches[1]);
                if ($user instanceof WP_User) {
                    return '#[pref-link id=' . intval($user->ID) . ' type=author pref-lang=' . $this->toLangCode . ']';
                }
            }
    

            //Try taxonomy term
            $cat_id = $this->get_category_id_by_url($normalized_url);

            if($cat_id){
                 return '#[pref-link id=' . intval($cat_id) . ' type=taxonomy pref-lang=' . $this->toLangCode . ']';
            }
        } else {
            //print_r("External link, not converted.\n");
        }
    
        return $href; // fallback — external or unknown internal
    }
    

    private function replaceLink($link) {
        $doc = new DOMDocument();
        libxml_use_internal_errors(true);
    
        // Заменили mb_convert_encoding на корректную загрузку UTF-8 HTML
        $doc->loadHTML('<?xml encoding="utf-8" ?>' . $link, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
        libxml_clear_errors();
    
        $tag = $doc->getElementsByTagName('a')->item(0);
    
        if ($tag) {
            $attributes = [];
            foreach ($tag->attributes as $attr) {
                $attributes[$attr->nodeName] = $attr->nodeValue;
            }
    
            // check for anchors
            if (isset($attributes['href']) && strpos($attributes['href'], '#') === 0) {
                return $link;
            }
    
            // replace href to new
            $attributes['href'] = $this->convertRawHrefToShortcode($attributes['href']);
    
            // save link inner HTML
            $linkText = '';
            foreach ($tag->childNodes as $child) {
                $linkText .= $doc->saveHTML($child);
            }
    
            // create a new tag
            $newLink = '<a';
            foreach ($attributes as $name => $value) {
                $newLink .= " {$name}=\"" . htmlspecialchars($value, ENT_QUOTES | ENT_SUBSTITUTE, 'UTF-8') . "\"";
            }
            $newLink .= ">{$linkText}</a>";
    
            return $newLink;
        } else {
            print_r('Tag <a> not found!');
        }
    }
    
    











    private function get_category_id_by_url(string $url): ?int {
        $parsed = parse_url($url);
    
        if (empty($parsed['path'])) {
            //print_r("No path found in URL: $url\n");
            return null;
        }
    
        $path = trim($parsed['path'], '/');
        $parts = explode('/', $path);
    
        if (empty($parts)) {
            //print_r("No slug found in path: $path\n");
            return null;
        }
    
        $slug = end($parts);
        //print_r("Category slug: $slug\n");
    
        $term = get_term_by('slug', $slug, 'category');
    
        if ($term instanceof WP_Term) {
            //print_r("Category ID: {$term->term_id}\n");
            return (int) $term->term_id;
        } else {
            //print_r(date('Y-m-d H:i:s') . " ❌ Category not found for slug: $slug\n");
        }
    
        return null;
    }


    public function translatePostMetaValues($post_id, $new_post_id) {
        $all_meta = get_post_meta($post_id);
        foreach ($all_meta as $meta_key => $meta_values) {
            foreach ($meta_values as $meta_value) {
                $original_value = maybe_unserialize($meta_value);
                
                if (is_string($original_value) && !empty(trim($original_value)) && !ctype_digit(trim($original_value))) {
                    $json_decoded = json_decode($original_value, true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($json_decoded)) {
                        $new_value = $original_value;
                    }
                    elseif (preg_match('/^https?:\/\//', $original_value) || stripos($original_value, 'iframe') !== false) {
                        $new_value = $original_value;
                    } else {
                        $new_value = $this->translate($original_value);
                    }
                } else {
                    $new_value = $original_value;
                }
                update_post_meta($new_post_id, $meta_key, $new_value);
            }
        }
    }
    


    function createTranslateCategories() {

        global $sitepress, $wpdb;


        $from_lang = $this->fromLangCode;
        $to_lang = $this->toLangCode;

        $args = [
            'taxonomy'   => 'category',
            'hide_empty' => false,
        ];

        try {
    

            do_action('wpml_switch_language', $this->fromLangCode);

            sleep(2);
            
            $terms = get_terms($args);

            if (empty($terms)) {
                $this->createLogRecord("❌ No categories to translate");
                return;
            }

     
            do_action('wpml_switch_language', $this->toLangCode);

            sleep(2);
        
            foreach ($terms as $term) {

                $translated_term_id = apply_filters('wpml_object_id', $term->term_id, 'category', false, $to_lang);
                if ($translated_term_id) {
                    $this->createLogRecord("ℹ️ Category already translated: " . $term->name);
                    continue;
                }
        
                $translated_name = $this->translate($term->name);
                $translated_description = $this->translate($term->description);
        
                $new_term = wp_insert_term($translated_name, 'category', [
                    'slug' => $this->transliterate_title($translated_name),
                    'description' => $translated_description,
                    'parent' => 0, // для вложенных категорий нужно перевести родителя и получить его ID
                ]);
        
                if (is_wp_error($new_term)) {
                    $this->createLogRecord("❌ Category creation error: " . $new_term->get_error_message());
                    continue;
                }

                $trid = $this->getWPMLtrid($term->term_taxonomy_id, 'tax_category');

                $set_language_args = array(
                    'element_id'    => $new_term['term_taxonomy_id'],
                    'element_type'  => 'tax_category',
                    'trid'          => $trid,
                    'language_code' => $this->toLangCode,
                    'source_language_code' => $this->fromLangCode,
                    'check_duplicates' => false
                );

                do_action( 'wpml_set_element_language_details', $set_language_args );

                $this->createLogRecord("✅ Category created: " . $translated_name);

            }
        }
        catch (\Throwable $e) {
            $this->createLogRecord("❌ Category creation error: " . $e->getMessage());
        }
    }




	private function translatePoFile(): void{
		$poTranslator = new \GPTTranslator\Services\PoFileTranslator(
			$this,
			$this->toLocale,
			$this->fromLocale,
			$this->toLangCode,
			$this->fromLangCode
		);

		$result = $poTranslator->translateThemePoFile();

		if($result){
			print_r("✅ File updated: $result\n");
		}else{
			print_r("❌ Failed to translate PO file\n");
		}
	}


    private function createTranslateMenus($task_id) {
        global $wpdb, $sitepress;
    
        // Переключаемся на исходный язык
        do_action('wpml_switch_language', $this->fromLangCode);
        sleep(3);
    
        // Получаем все меню и фильтруем только исходные (не переводы)
        $allMenus = wp_get_nav_menus();
        $source_lang_menus = [];
        $menu_translation_map = [];
    
        if (!empty($allMenus)) {
            foreach ($allMenus as $menu) {
                $translatedMenuID = apply_filters('wpml_object_id', $menu->term_id, 'nav_menu', false);
                if ($translatedMenuID == $menu->term_id) {
                    $source_lang_menus[$menu->term_id] = $menu;
                }
            }
        }
    
        // Переключаемся на целевой язык для создания переводов
        do_action('wpml_switch_language', $this->toLangCode);
        sleep(3);
    
        // Создаем переведенные меню
        foreach ($source_lang_menus as $key => $menu) {
            $trid = $this->getWPMLtrid($menu->term_taxonomy_id, 'tax_nav_menu');
    
            if (!$trid) {
                $this->createLogRecord("❌ TRID not found for menu: '{$menu->name}' (ID: $key)");
                $trid = $menu->term_id;
            }
    
            $new_menu_name = $menu->name . " ({$this->toLangCode})";
            $translated_menu = wp_insert_term($new_menu_name, 'nav_menu');
    
            $this->createLogRecord("✅ Menu created: " . $new_menu_name);
    
            if (is_wp_error($translated_menu)) {
                $this->createLogRecord("❌ Error creating menu: " . $translated_menu->get_error_message());
                continue;
            }
    
            $translated_term_id = $translated_menu['term_id'];
            $translated_term_taxonomy_id = $translated_menu['term_taxonomy_id'];
            
            // Сохраняем соответствие оригинального и переведенного меню
            $menu_translation_map[$menu->term_id] = $translated_term_id;
    
            // Устанавливаем языковые данные для WPML
            $set_language_args = array(
                'element_id' => $translated_term_taxonomy_id,
                'element_type' => 'tax_nav_menu',
                'trid' => $trid,
                'language_code' => $this->toLangCode,
                'source_language_code' => $this->fromLangCode,
            );
    
            do_action('wpml_set_element_language_details', $set_language_args);
            
            // Создаем элементы меню
            $this->createMenuItems($menu->term_id, $translated_term_id);
            
            sleep(3);
        }
    
        // Обновляем расположения меню
        $this->updateMenuLocations($menu_translation_map);
    }






    
    private function createMenuItems($source_menu_id, $translated_menu_id) {
        // 1. Получаем элементы исходного меню на исходном языке
        do_action('wpml_switch_language', $this->fromLangCode);
        $menu_items = wp_get_nav_menu_items($source_menu_id);
        
        if (empty($menu_items)) {
            $this->createLogRecord("❌ No menu items found for menu ID: $source_menu_id");
            return;
        }
    
        // 2. Переключаемся на целевой язык перед созданием элементов
        do_action('wpml_switch_language', $this->toLangCode);
        
        $item_map = array();
        
        // Сначала создаем все элементы
        foreach ($menu_items as $menu_item) {
            $new_item_id = $this->createMenuItem($menu_item, $translated_menu_id);
            
            if ($new_item_id) {
                $item_map[$menu_item->ID] = $new_item_id;
            }
        }
        
        // Затем обновляем parent-child отношения
        foreach ($menu_items as $menu_item) {
            if ($menu_item->menu_item_parent && isset($item_map[$menu_item->menu_item_parent])) {
                wp_update_post(array(
                    'ID' => $item_map[$menu_item->ID],
                    'menu_item_parent' => $item_map[$menu_item->menu_item_parent]
                ));
            }
        }
        
        // Возвращаемся к исходному языку
        do_action('wpml_switch_language', $this->fromLangCode);
    }
    
    private function createMenuItem($source_item, $translated_menu_id) {
        // Важно: мы уже должны быть на целевом языке
        
        $args = array(
            'menu-item-title'     => $this->translateMenuItemTitle($source_item->title),
            'menu-item-position' => $source_item->menu_order,
            'menu-item-status'   => 'publish',
            'menu-item-type'     => $source_item->type,
        );
    
        switch ($source_item->type) {
            case 'post_type':
            case 'taxonomy':
            case 'post_type_archive':
                $translated_id = apply_filters(
                    'wpml_object_id', 
                    $source_item->object_id, 
                    $source_item->object, 
                    false, 
                    $this->toLangCode
                );
                
                if ($translated_id) {
                    $args['menu-item-object-id'] = $translated_id;
                    $args['menu-item-object'] = $source_item->object;
                } else {
                    $this->createLogRecord("⚠️ No translation for {$source_item->object} ID: {$source_item->object_id}");
                    return false;
                }
                break;
                
            case 'custom':
                $args['menu-item-url'] = $this->translateMenuItemUrl($source_item->url);
                break;
        }
    
        // Явно указываем ID целевого меню
        $new_item_id = wp_update_nav_menu_item($translated_menu_id, 0, $args);
        
        if (is_wp_error($new_item_id)) {
            $this->createLogRecord("❌ Error creating menu item: " . $new_item_id->get_error_message());
            return false;
        }
    
        $this->copyMenuItemMeta($source_item->ID, $new_item_id);
        
        // $this->createLogRecord("✅ Created menu item '{$args['menu-item-title']}' in menu ID: $translated_menu_id");
        
        return $new_item_id;
    }
    
    private function updateMenuLocations($menu_translation_map) {
        if (empty($menu_translation_map)) return;
    
        // Переключаемся на исходный язык для получения расположений
        do_action('wpml_switch_language', $this->fromLangCode);
        $original_locations = get_theme_mod('nav_menu_locations');
        
        if (!$original_locations) return;
    
        $new_locations = array();
        
        foreach ($original_locations as $location => $menu_id) {
            if (isset($menu_translation_map[$menu_id])) {
                $new_locations[$location] = $menu_translation_map[$menu_id];
            }
        }
        
        if (!empty($new_locations)) {
            // Переключаемся на целевой язык для обновления расположений
            do_action('wpml_switch_language', $this->toLangCode);
            set_theme_mod('nav_menu_locations', $new_locations);
            
            // Возвращаемся к исходному языку
            do_action('wpml_switch_language', $this->fromLangCode);
            
            $this->createLogRecord("✅ Updated menu locations for language: {$this->toLangCode}");
        }
    }
    
    private function translateMenuItemTitle($title) {
        // Здесь может быть ваша логика перевода
        return $this->translate($title) ?: $title;
    }
    
    private function translateMenuItemUrl($url) {
        if ($this->isInternalURL($url)) {
            $parsed_url = parse_url($url);
            
            if (!isset($parsed_url['host']) && isset($parsed_url['path'])) {
                // Для относительных URL
                $translated_url = apply_filters('wpml_permalink', home_url($parsed_url['path']), $this->toLangCode);
                $translated_path = parse_url($translated_url, PHP_URL_PATH);
                
                // Сохраняем query и fragment если есть
                $query = isset($parsed_url['query']) ? '?' . $parsed_url['query'] : '';
                $fragment = isset($parsed_url['fragment']) ? '#' . $parsed_url['fragment'] : '';
                
                return $translated_path . $query . $fragment;
            }
        }
        
        return $url;
    }
    
    private function copyMenuItemMeta($source_item_id, $new_item_id) {
        $meta_keys = array(
            '_menu_item_classes',
            '_menu_item_target',
            '_menu_item_xfn',
            '_menu_item_aria_label',
            '_menu_item_description',
            '_wiki_listing_title',
            '_wp_old_date',
            '_menu_auto_more'
        );
        
        foreach ($meta_keys as $meta_key) {
            $meta_value = get_post_meta($source_item_id, $meta_key, true);
            if (!empty($meta_value) || $meta_value === '0') {
                update_post_meta($new_item_id, $meta_key, $meta_value);
            }
        }
    }






    
    private function translateUsersDescription() {

        do_action('wpml_switch_language', $this->fromLangCode);

        $users = get_users();
        
        foreach ($users as $user) {
            $user_id = $user->ID;
            $original_description = get_the_author_meta('description', $user_id);

            if (empty($original_description)) {
                continue;
            }
            
            $translation = $this->translate($original_description);
            
            // Select domain (context) and unique name for translation string
            $context = 'Authors';
            $string_name = 'description_' . $user_id;
            
            // Register original string in wpml string translation
            if (function_exists('icl_register_string')) {
                icl_register_string($context, $string_name, $original_description);
            }
            
            // Get id of registered string
            $string_id = function_exists('icl_get_string_id')
                ? icl_get_string_id($original_description, $context, $string_name)
                : null;
            
            // Add translation
            if ($string_id && function_exists('icl_add_string_translation')) {
                icl_add_string_translation($string_id, $this->toLangCode, $translation, ICL_TM_COMPLETE);
            }
        }
    }

    private function translateOptions() {

        global $sitepress;

        $fields_to_translate = ['title', 'description', 'link_title'];
    
        $recursive_translate = function($data) use (&$recursive_translate, $fields_to_translate) {
            foreach ($data as $key => $value) {

                if (is_array($value)) {
                    $data[$key] = $recursive_translate($value);
                } else {

                    if (in_array($key, $fields_to_translate, true) && is_string($value)) {
                        $data[$key] = $this->translate($value);
                    }

                    if ( ('link' === $key || 'data_protection_declaration_page' === $key) && is_string($value) ) {
                        $post_id = $this->getPostIdFromUrl($value);

                        if ($post_id) {
                            $page = get_post($post_id);
                        
                            if ($page) {
                                // Получаем ID перевода
                                $translated_post_id = apply_filters('wpml_object_id', $page->ID, $page->post_type, false, $this->toLangCode);
                                $target_post = get_post($translated_post_id);
                        
                                if ($target_post) {
                                    $target_post_name = $target_post->post_name;
                                    $data[$key] = "/{$this->toLangCode}/{$target_post_name}";
                                } else {
                                    // Нет перевода, оставляем исходный URL
                                    $data[$key] = $value;
                                }
                            } else {
                                // Пост с таким ID не найден
                                $data[$key] = $value;
                            }
                        } else {
                            // $post_id пустой или невалидный
                            $data[$key] = $value;
                        }
                        
                    }
                }
            }
            return $data;
        };
    

        $source_options = get_option('theme_settings_' . $this->fromLangCode);


        if ($source_options !== false) {
            $translated_options = $recursive_translate($source_options);
            update_option('theme_settings_' . $this->toLangCode, $translated_options);
        }
    }

    private function translateBorlabsDialog() {
        global $wpdb, $sitepress;

        $prefixes = [
            'settings_config'  => 'BorlabsCookieDialogSettingsConfig_',
            'style_version'    => 'BorlabsCookieStyleVersion_',
            'style_config'     => 'BorlabsCookieDialogStyleConfig_',
            'widget_config'    => 'BorlabsCookieWidgetConfig_',
            'config'           => 'BorlabsCookieConfig_',
            'config_version'   => 'BorlabsCookieConfigVersion_',
            'localization'     => 'BorlabsCookieDialogLocalization_',
            'group'            => 'BorlabsCookieGroup_',
            'provider'         => 'BorlabsCookieProvider_',
            'content_blocker'  => 'BorlabsCookieContentBlocker_',
            'service'          => 'BorlabsCookieService_'
        ];

        $values = [];

        foreach ($prefixes as $key => $prefix) {
            $values[$key] = get_option($prefix . $this->fromLangCode);
        }

       
        if (isset($values['localization']) && is_object($values['localization'])) {
            try {
                $refObject = new \ReflectionObject($values['localization']);

                foreach ($refObject->getProperties() as $property) {
                    $property->setAccessible(true);
                    $value = $property->getValue($values['localization']);

                    if (is_string($value)) {
                        $translated_value = $this->translate($value);
                        $property->setValue($values['localization'], $translated_value);
                    }
                }
            } catch (\Throwable $e) {
                print_r("❌ Error processing localization object: " . $e->getMessage() . "\n");
                $this->createLogRecord("❌ Error processing localization object: " . $e->getMessage());
                return false;
            }
        } else {
            print_r("❌ Error: localization is not an object or doesn't exist.\n");
            $this->createLogRecord("❌ Error: localization is not an object or doesn't exist.");
            return false;
        }

        foreach ($prefixes as $key => $prefix) {
            if (isset($values[$key])) {
                update_option($prefix . $this->toLangCode, $values[$key]);

                // Set autoload = no
                $wpdb->update(
                    $wpdb->options,
                    ['autoload' => 'no'],
                    ['option_name' => $prefix . $this->toLangCode]
                );
            }
        }

        print_r("🚀 Translating Borlabs service groups\n");
        $this->createLogRecord("🚀 Translating Borlabs service groups ..........");

        $groupTable = 'logic_borlabs_cookie_service_groups';
        $oldGroups = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$groupTable} WHERE language = %s",
            $this->fromLangCode
        ));
        
        $groupIdMap = [];
        
        if (!empty($oldGroups)) {
            foreach ($oldGroups as $group) {
              
                if (!isset($group->key) || !isset($group->id)) {
                    print_r("⚠️ Skipping invalid group record\n");
                    $this->createLogRecord("⚠️ Skipping invalid group record");
                    continue;
                }
                
                // Check if group exists
                $existingGroup = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT * FROM {$groupTable} WHERE `key` = %s AND language = %s",
                        $group->key,
                        $this->toLangCode
                    )
                );
            
                if ($existingGroup) {
                    // Skip translation if group already exists
                    $groupName = isset($group->name) ? $group->name : 'unnamed';
                    print_r("ℹ️ Service group '{$groupName}' already exists, skipping translation\n");
                    $this->createLogRecord("ℹ️ Service group '{$groupName}' already exists, skipping translation");
                    $groupIdMap[$group->id] = $existingGroup->id;
                    continue;
                }
            
                $newGroup = clone $group;
                unset($newGroup->id);
                $newGroup->language = $this->toLangCode;
            
                if (isset($newGroup->description) && !empty($newGroup->description)) {
                    $newGroup->description = $this->translate($newGroup->description);
                }
            
                if (isset($newGroup->name) && !empty($newGroup->name)) {
                    $newGroup->name = $this->translate($newGroup->name);
                }
            
                $result = $wpdb->insert($groupTable, (array)$newGroup);
            
                if ($result !== false) {
                    $groupIdMap[$group->id] = $wpdb->insert_id;
                } else {
                    print_r("❌ Error inserting service group ID {$group->id}: {$wpdb->last_error}\n");
                    $this->createLogRecord("❌ Error inserting service group ID {$group->id}: {$wpdb->last_error}");
                }
            }
        }


        print_r("🚀 Translating Borlabs services\n");
        $this->createLogRecord("🚀 Translating Borlabs services ..........");

        $serviceTable = 'logic_borlabs_cookie_services';
        $oldServices = $wpdb->get_results($wpdb->prepare(
            "SELECT * FROM {$serviceTable} WHERE language = %s",
            $this->fromLangCode
        ));
        
        if (!empty($oldServices)) {
            foreach ($oldServices as $service) {
              
                if (!isset($service->key) || !isset($service->id) || !isset($service->service_group_id)) {
                    print_r("⚠️ Skipping invalid service record\n");
                    $this->createLogRecord("⚠️ Skipping invalid service record");
                    continue;
                }
                
                // Check if service exists
                $existingService = $wpdb->get_row(
                    $wpdb->prepare(
                        "SELECT * FROM {$serviceTable} WHERE `key` = %s AND language = %s",
                        $service->key,
                        $this->toLangCode
                    )
                );
            
                if ($existingService) {
                    // Skip translation if service already exists
                    $serviceName = isset($service->name) ? $service->name : 'unnamed';
                    print_r("ℹ️ Service '{$serviceName}' already exists, skipping translation\n");
                    $this->createLogRecord("ℹ️ Service '{$serviceName}' already exists, skipping translation");
                    continue;
                }
            
                $newService = clone $service;
                unset($newService->id);
                $newService->language = $this->toLangCode;
            
                if (isset($groupIdMap[$service->service_group_id])) {
                    $newService->service_group_id = $groupIdMap[$service->service_group_id];
                } else {
                    print_r("⚠️ Skipping service ID {$service->id}: group ID not found.\n");
                    $this->createLogRecord("⚠️ Skipping service ID {$service->id}: group ID not found");
                    continue;
                }
            
                $serviceName = isset($service->name) ? $service->name : '';
                $serviceDesc = isset($service->description) ? $service->description : '';
                
                $translatedName = !empty($serviceName) ? $this->translate($serviceName) : '';
                $translatedDescription = !empty($serviceDesc) ? $this->translate($serviceDesc) : '';
                
                $newService->name = $translatedName;
                $newService->description = $translatedDescription;
            
                $result = $wpdb->insert($serviceTable, (array)$newService);
            
                if ($result === false) {
                    print_r("❌ Error inserting service ID {$service->id}: {$wpdb->last_error}\n");
                    $this->createLogRecord("❌ Error inserting service ID {$service->id}: {$wpdb->last_error}");
                }
            }
        }

        print_r("🚀 Translating Borlabs tables\n");
        $this->createLogRecord("🚀 Translating Borlabs tables ..........");

        
        // All the rest tables
        $this->duplicateAndTranslateRows(
            [
                'logic_borlabs_cookie_groups',
                'logic_borlabs_cookie_providers',
                'logic_borlabs_cookie_cookies',
                'logic_borlabs_cookie_content_blockers',
                'logic_borlabs_cookie_content_blocker',
            ],
            ['description']
        );








        print_r("🚀 Translating BorlabsCookieDialogSettingsConfig_\n");
        $this->createLogRecord("🚀  Translating BorlabsCookieDialogSettingsConfig ..........");

        // Translate URLs
        $option_name = 'BorlabsCookieDialogSettingsConfig_' . $this->toLangCode;
    
        // Get settings object
        $option_value = get_option($option_name);
    
        if (!$option_value) {
            print_r("⚠️ Settings not found for {$option_name}\n");
            $this->createLogRecord("⚠️ Settings not found for {$option_name}");
            return true;
        }
    
        // List of single fields to localize
        $fields_to_localize = [
            'privacyPageCustomUrl',
            'privacyPageUrl',
            'imprintPageCustomUrl',
            'imprintPageUrl',
        ];


      
    
        $prod_url = 'https://www.sellerlogic.com';

        foreach ($fields_to_localize as $field) {
        
            $url = $option_value->$field;
            $parsed = parse_url($url);
            $slug = isset($parsed['path']) ? basename($parsed['path']) : '';
            $page = get_page_by_path($slug);
            $original_permalink = get_permalink($page->ID); 
            $translated_permalink = apply_filters('wpml_permalink', $original_permalink, $this->fromLangCode, true);

           
            if (!empty($translated_permalink)) {
                $parsed_url = parse_url($translated_permalink);
                if ($parsed_url === false) {
                    continue;
                }
                $new_url = rtrim($prod_url, '/') . (isset($parsed_url['path']) ? $parsed_url['path'] : '');
                if (!empty($parsed_url['query'])) {
                    $new_url .= '?' . $parsed_url['query'];
                }
                if (!empty($parsed_url['fragment'])) {
                    $new_url .= '#' . $parsed_url['fragment'];
                }
                $option_value->$field = $new_url;
            }
        }
        
        if (!empty($option_value->hideDialogOnPages) && is_array($option_value->hideDialogOnPages)) {
            $translated_urls = [];
        
            foreach ($option_value->hideDialogOnPages as $url) {
                $parsed = parse_url($url);
                $slug = isset($parsed['path']) ? basename($parsed['path']) : '';
                $page = get_page_by_path($slug);
                $original_permalink = get_permalink($page->ID);
                $translated_permalink = apply_filters('wpml_permalink', $original_permalink, $this->fromLangCode, true);
        
                if (!empty($translated_permalink)) {
                    $parsed_url = parse_url($translated_permalink);
                    if ($parsed_url === false) {
                        continue;
                    }
                    $new_url = rtrim($prod_url, '/') . (isset($parsed_url['path']) ? $parsed_url['path'] : '');
                    if (!empty($parsed_url['query'])) {
                        $new_url .= '?' . $parsed_url['query'];
                    }
                    if (!empty($parsed_url['fragment'])) {
                        $new_url .= '#' . $parsed_url['fragment'];
                    }
                    $translated_urls[] = $new_url;
                }
            }
        
            $option_value->hideDialogOnPages = $translated_urls;
        }
        
    
        update_option($option_name, $option_value);

        return true;
    }
    
    private function duplicateAndTranslateRows(array $tables, array $fieldsToTranslate): void {
        global $wpdb;

        foreach ($tables as $table) {

            // Get all 'en' rows
            $rows = $wpdb->get_results(
                $wpdb->prepare("SELECT * FROM {$table} WHERE language = %s", $this->fromLangCode),
                ARRAY_A
            );

            foreach ($rows as $row) {
                unset($row['id']); // delete ID autoincrement
                $row['language'] = $this->toLangCode;

                foreach ($fieldsToTranslate as $field) {
                    if (!empty($row[$field]) && is_string($row[$field])) {
                        $row[$field] = $this->translate($row[$field]);
                    }
                }

                $wpdb->insert($table, $row);
            }
        }
    }





    // Utilities

    private function transliterate_title($title) {
        $map = [
            'а'=>'a','б'=>'b','в'=>'v','г'=>'g','д'=>'d','е'=>'e','ё'=>'e','ж'=>'zh','з'=>'z','и'=>'i','й'=>'y',
            'к'=>'k','л'=>'l','м'=>'m','н'=>'n','о'=>'o','п'=>'p','р'=>'r','с'=>'s','т'=>'t','у'=>'u','ф'=>'f',
            'х'=>'h','ц'=>'ts','ч'=>'ch','ш'=>'sh','щ'=>'shch','ъ'=>'','ы'=>'y','ь'=>'','э'=>'e','ю'=>'yu','я'=>'ya',
            'А'=>'A','Б'=>'B','В'=>'V','Г'=>'G','Д'=>'D','Е'=>'E','Ё'=>'E','Ж'=>'Zh','З'=>'Z','И'=>'I','Й'=>'Y',
            'К'=>'K','Л'=>'L','М'=>'M','Н'=>'N','О'=>'O','П'=>'P','Р'=>'R','С'=>'S','Т'=>'T','У'=>'U','Ф'=>'F',
            'Х'=>'H','Ц'=>'Ts','Ч'=>'Ch','Ш'=>'Sh','Щ'=>'Shch','Ъ'=>'','Ы'=>'Y','Ь'=>'','Э'=>'E','Ю'=>'Yu','Я'=>'Ya',
        ];
        $title = strtr($title, $map);
        return sanitize_title($title);
    }
    

    private function normalize_content($content) {
        // Если строка не в UTF-8, преобразуем её в UTF-8.
        if (!mb_check_encoding($content, 'UTF-8')) {
            $content = mb_convert_encoding($content, 'UTF-8', 'auto');
        }
        // Удаляем невалидные символы.
        $content = iconv('UTF-8', 'UTF-8//IGNORE', $content);

        $normalized = <<<HTML
$content
HTML;
            
            $normalized = (string) $normalized;
            $normalized = wp_slash($normalized);


        return $normalized;
    }


    
    public function isInternalURL($url): bool {
        $parsed_url = parse_url($url);
    
        // Относительный URL → считаем внутренним
        if (!isset($parsed_url['host']) || empty($parsed_url['host'])) {
            return true;
        }
    
        // Получаем хост из переданного URL
        $host = preg_replace('/^www\./', '', $parsed_url['host']);
    
        // Получаем текущий хост сайта
        $site_host = parse_url(home_url(), PHP_URL_HOST);
        $site_host = preg_replace('/^www\./', '', $site_host);
    
        return strtolower($host) === strtolower($site_host);
    }
    
    
    public function getWPMLtrid($post_id, $element_type) {
        global $sitepress;
        
        $trid = $sitepress->get_element_trid($post_id, $element_type);

        if (!$trid) {
            $this->createLogRecord("⚠️ TRID not found for the element (ID: $post_id, Type: $element_type). Using ID as TRID.");
            $trid = $post_id; 
        } 
        
        return $trid;
    }
    

    public function getToLangCategories($post_id) {
        global $wpdb, $sitepress;
        
        do_action('wpml_switch_language', $this->fromLangCode);
        sleep(3);
       
        $terms = wp_get_post_terms($post_id, 'category', ['fields' => 'ids']);
    
        do_action('wpml_switch_language', $this->toLangCode);
        sleep(3);
        $translated_term_ids = [];
    

        foreach ($terms as $term_id) {
            $translated_term_id = apply_filters('wpml_object_id', $term_id, 'category', false, $this->toLangCode);
            $term = get_term($term_id);

            if ($translated_term_id) {
                $translated_term_ids[] = $translated_term_id;
                //$this->createLogRecord(" Translated category founded: '{$term->name}' (ID: $term_id)");
            } else {
                $this->createLogRecord("⚠️ No translation found for category '{$term->name}' (ID: $term_id)");
            }
        }
        
        return $translated_term_ids;
    }
    
    public function getLangData($langName)
    {
        $languages = apply_filters('wpml_active_languages', NULL, ['skip_missing' => 0]);
        if (!is_array($languages)) {
            return null;
        }

        foreach ($languages as $lang) {
            if (strcasecmp(trim($lang['language_code']), trim($langName)) === 0) {
                return [
                    'locale' => $lang['default_locale'],
                    'name'   => $lang['translated_name']
                ];
            }
        }
        return null;
    }

    private function getTermFromUrl($url) {
        $taxonomies = get_taxonomies(['public' => true], 'names');
        $term = null;
        foreach ($taxonomies as $taxonomy) {
            $terms = get_terms([
                'taxonomy' => $taxonomy,
                'hide_empty' => false,
            ]);
    
            foreach ($terms as $t) {
                $term_link = get_term_link($t);

                $parsed_url = parse_url($term_link);
                $path = isset($parsed_url['path']) ? $parsed_url['path'] : '';

                if ($path === $url) {
                    return $t;
                }
            }
        }
    
        return null;
    }










    // Translate

    public function translate(string $text): string
    {

        // Do not translate:
        // ""
        // " "
        // "123"
        // "-42"
        // "1.5"
        // "1,5"
        // " -42 "
        // time-like strings like "1741880029:57"
        // dates like "2020-11-24"
        
        if (trim($text) === '' || 
            preg_match('/^-?\d+[.,]?\d*\s*$/', trim($text)) || // for numbers like "123", "-42", "1.5"
            preg_match('/^\d+:\d+$/', trim($text)) || // for time-like strings like "1741880029:57"
            preg_match('/^\d{4}-\d{2}-\d{2}$/', trim($text))) { // for date-like strings like "2020-11-24"
            return $text; // Don't translate this
        }
        
        $text = $this->replaceInternalLinksWithShortcode($text); 

        $protectedText = $this->protectNonTranslatableWords($text);

        $translated = $this->translateText($protectedText);

        return $this->restoreProtectedWords($translated);
    }

    // Non translatable words


    protected function protectNonTranslatableWords(string $text): string {
        foreach ($this->nonTranslatableMap as $word => $token) {
            $escapedWord = preg_quote($word, '/');
            $text = preg_replace("/{$escapedWord}/u", $token, $text);
        }
        return $text;
    }
    
    

    protected function restoreProtectedWords(string $text): string {
        foreach ($this->reverseNonTranslatableMap as $token => $word) {
            $text = str_replace($token, $word, $text);
        }
        return $text;
    }
    


    // API

    public function translateText(string $text)
    {
        if (trim($text) === '') {
            return $text;
        }

       print_r(date('Y-m-d H:i:s') . " 💬 " . $text . "\n");
       //$this->createLogRecord(" 💬 " . $text);

        try {
            if ($this->threadId === null || $this->translationCount > 50) {  // Create new thread each 50 messages due to context brokes
                $this->threadId = $this->createThread();
                $this->translationCount = 0; 
            }
            
            sleep(2); 

            $this->addMessageToThread($this->threadId, $text);
            $runId = $this->createRun($this->threadId);
            $this->pollRunStatus($this->threadId, $runId);
            $this->translationCount++; 
            return $this->getMessageFromThread($this->threadId, $runId, $text);
    
        } catch (\Throwable $e) {
            print_r(date('Y-m-d H:i:s') . "❌ Translation failed: " . $e->getMessage() . "\n");
            $this->createLogRecord("❌ Translation failed: " . $e->getMessage());
            $this->threadId = null;
            $this->translationCount = 0;
            sleep(3);
            return $this->translateText($text);
        }
    }


    private function pollRunStatus(string $threadId, string $runId, int $timeout = 120): void {
        $startTime = time();
    
        while (time() - $startTime < $timeout) {
            $status = $this->getRunStatus($threadId, $runId);
            print_r(date('Y-m-d H:i:s') . " ⏳ Polling status: $status\n");
            //$this->createLogRecord(" ⏳ Polling status: $status");
    
            if ($status === 'completed') {
                return; 
            }
    
            if (in_array($status, ['failed', 'cancelled', 'expired'])) {
                throw new \Exception("Run $runId failed with status: $status");
                print_r(date('Y-m-d H:i:s') . "❌ Run $runId failed with status: $status\n");
            }
    
            sleep(3); 
        }
    
        throw new \Exception("Timeout waiting for run $runId to complete.");
        print_r(date('Y-m-d H:i:s') . "❌ Timeout waiting for run $runId to complete.\n");
    }

    private function getRunStatus(string $threadId, string $runId): ?string {
        $url = "https://api.openai.com/v1/threads/{$threadId}/runs/{$runId}";
    
        $response = wp_remote_get($url, [
            'headers' => [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'OpenAI-Beta'   => 'assistants=v2',
            ],
            'timeout' => 30,
        ]);
    
        if (is_wp_error($response)) {
            $this->createLogRecord("❌ Error polling run status: " . $response->get_error_message());
            return null;
        }
    
        $body = json_decode(wp_remote_retrieve_body($response), true);
        return $body['status'] ?? null;
    }
        

    public function executeRequest(string $method, string $url, array $data = []): array
    {
        $attempt = 0;
        $response = [];

        while ($attempt < self::LOOP_MAX_ATTEMPTS) {
            sleep(2);
            $response = $this->sendRequest($method, $url, $data);
            if (!empty($response) && empty($response['error'])) {
                return $response;
            }
            $attempt++;
            $error = "Attempt {$attempt} for {$url} failed. Response: " . json_encode($response);
            print_r(date('Y-m-d H:i:s') . "❌ " . $error . "\n");
            $this->createLogRecord("❌ " . $error);
        }
        return $response;
    }

    public function createThread()
    {

       
        
        $data = [
            'messages' => [
                [
                    'role' => 'user',
                    'content' => "
                        You are a professional translator.
                        Translate everything in the following input into '{$this->toLangName}' — including questions, but do NOT answer them. Just translate them literally.
                        You must never generate explanations, summaries, or answers.
                        Do not interpret the text — just translate it word for word.

                        The text will contain HTML — keep all HTML tags, attributes, whitespace, links, and formatting 100% unchanged.
                        Never modify the HTML structure or content.

                        All geographic names must be translated to '{$this->toLangName}'.

                        Translate everything else — including HTML attributes (like title, alt, placeholder, etc).
                        If you can't translate, return the input exactly as-is.",
                ],
            ],
        ];


        //print_r(date('Y-m-d H:i:s') . " ℹ️ {$data['messages'][0]['content']}\n");

        $response = $this->executeRequest('POST', self::THREADS_API_URL, $data);

        if (empty($response['id'])) {
            $this->createLogRecord("⚠️ Failed to create thread. Response: " . json_encode($response));
            print_r("⚠️ Failed to create thread. Response: " . json_encode($response));
        }
        return $response['id'];
    }

    public function addMessageToThread(string $threadId, string $text): void
    {
        $url = str_replace('{thread_id}', $threadId, self::MESSAGES_API_URL);
        $this->executeRequest('POST', $url, [
            'role' => 'user',
            'content' => $text,
        ]);
    }
    
    public function createRun(string $threadId): string
    {
        $url = str_replace('{thread_id}', $threadId, self::RUNS_API_URL);
        $data = ['assistant_id' => $this->assistantId];
    
        for ($attempt = 1; $attempt <= self::LOOP_MAX_ATTEMPTS; $attempt++) {
            $response = $this->executeRequest('POST', $url, $data);
            $runId = $response['id'] ?? '';
    
            if (!empty($runId)) {
                return $runId;
            }
    
            $error = "Failed to create run (attempt {$attempt}). Response: " . json_encode($response);
            print_r(date('Y-m-d H:i:s') . "❌ {$error}\n");
            $this->createLogRecord(" ❌ {$error}");
            sleep(2);
        }
    
        $error = date('Y-m-d H:i:s') . "❌ Exceeded max attempts in createRun for thread {$threadId}.";
        print_r("{$error}\n");
        $this->createLogRecord($error);
        return '';
    }

    public function getMessageFromThread(string $threadId, string $runId, string $originalText, int $retryCount = 0): string
    {
        if (empty($runId)) {
            print_r(date('Y-m-d H:i:s') . "❌ Cannot fetch messages: runId is empty.\n");
            $this->createLogRecord("❌ Cannot fetch messages: runId is empty.");
            return $originalText;
        }
    
        $url = "https://api.openai.com/v1/threads/{$threadId}/messages?run_id={$runId}";
      
    
        for ($attempt = 1; $attempt <= self::LOOP_MAX_ATTEMPTS; $attempt++) {
            $response = $this->executeRequest('GET', $url);
            $message = $response['data'][0]['content'][0]['text']['value'] ?? '';
    
            sleep(2);

            if (!empty($message)) {
                $this->updateQueueTime($this->task_id, $this->parent_task_id);
                print_r(date('Y-m-d H:i:s') . " 🤖 {$message}\n");
                //$this->createLogRecord(" 🤖 " . $message);
                return $message;
            }
            sleep(2);
            print_r(date('Y-m-d H:i:s') . " ❌ Attempt {$attempt} failed to fetch translated message. Response: " . json_encode($response) . "\n");
            $this->createLogRecord(" ❌ Attempt {$attempt} failed to fetch translated message. Response: " . json_encode($response));
        }
    
        if ($retryCount < self::LOOP_MAX_ATTEMPTS) {
            print_r(date('Y-m-d H:i:s') . "⚠️ Retrying with new thread. Retry count: {$retryCount}.\n");
    
            $newThreadId = $this->createThread();
            $this->addMessageToThread($newThreadId, $originalText);
            $newRunId = $this->createRun($newThreadId);
    
            if (!empty($newRunId)) {
                $this->pollRunStatus($newThreadId, $newRunId);
            }
    
            return $this->getMessageFromThread($newThreadId, $newRunId, $originalText, $retryCount + 1);
        }
    
        print_r(date('Y-m-d H:i:s') . "❌ Maximum retries ({$retryCount}) reached. Returning original text.\n");
        $this->createLogRecord("❌ Maximum retries ({$retryCount}) reached. Returning original text.");
        return $originalText;
    }
    
    public function sendRequest(string $method, string $url, array $data = []): array
    {
        $args = [
            'headers' => [
                'Authorization' => "Bearer {$this->apiKey}",
                'Content-Type'  => 'application/json',
                'OpenAI-Beta'   => 'assistants=v2',
            ],
            'timeout' => 120,
        ];
    
        if ($method === 'POST') {
            $args['body'] = json_encode($data);
        }
    
        $response = $method === 'GET' ? wp_remote_get($url, $args) : wp_remote_post($url, $args);
    
        if (is_wp_error($response)) {
            $errorMessage = "❌ Request to {$url} failed. Error: " . $response->get_error_message();
            $this->createLogRecord($errorMessage);
            print_r(date('Y-m-d H:i:s') . "❌ {$errorMessage}\n");
            return [];
        }
    
        $status_code = wp_remote_retrieve_response_code($response);
        $body = wp_remote_retrieve_body($response);
        $decodedResponse = json_decode($body, true);
    
       
        if ($status_code === 400) {
            $errorDetails = $decodedResponse['error']['message'] ?? 'No additional error details';
            print_r(date('Y-m-d H:i:s') . "❌ Bad Request (400) - creating new thread: {$errorDetails}\n");
            $this->createLogRecord("❌ Bad Request (400) - creating new thread: {$errorDetails}");

            $this->threadId = null; 
            $this->translationCount = 0;
            return [];
        }
    
        if ($status_code === 429) {
            print_r(date('Y-m-d H:i:s') . "❌ Rate limited (429). Sleeping 10s...\n");
            $this->createLogRecord("❌ Rate limited (429). Sleeping 10s...");
            sleep(5);
            return [];
        }
        
      
        if (!in_array($status_code, [200, 201, 204])) {
            print_r(date('Y-m-d H:i:s') . "❌ API request failed with status {$status_code}\n");
            $this->createLogRecord("❌ API request failed with status {$status_code}");
            return [];
        }
    
        return $decodedResponse ?: [];
    }

}











