<?php

namespace GPTTranslator\Services;

use Gettext\Generator\PoGenerator;
use Gettext\Loader\PoLoader;
use Gettext\Translation;
use Gettext\Translations;

/**
 * Class for working with PO file translation
 */
class PoFileTranslator{
	private $translator;
	private $logger;
	private $toLocale;
	private $fromLocale;
	private $toLangCode;
	private $fromLangCode;

	/**
	 * @var Translations
	 */
	private $translations;

	/**
	 * Class constructor
	 *
	 * @param \SimpleOpenAITranslator $translator   Object for text translation
	 * @param string                  $toLocale     Target locale (e.g., 'de_DE')
	 * @param string                  $fromLocale   Source locale (e.g., 'en_US')
	 * @param string                  $toLangCode   Target language code (e.g., 'de')
	 * @param string                  $fromLangCode Source language code (e.g., 'en')
	 */
	public function __construct(
		\SimpleOpenAITranslator $translator,
		string $toLocale,
		string $fromLocale,
		string $toLangCode,
		string $fromLangCode
	){
		$this->translator = $translator;
		$this->toLocale = $toLocale;
		$this->fromLocale = $fromLocale;
		$this->toLangCode = $toLangCode;
		$this->fromLangCode = $fromLangCode;
		$this->logger = [$translator, 'createLogRecord']; // Use logging method from the main class
	}

	/**
	 * Translates theme PO file
	 *
	 * @param string $themeSlug Theme slug (default 'sellerlogic_theme')
	 *
	 * @return bool|string File path on success, false on error
	 */
	public function translateThemePoFile(string $themeSlug = 'sellerlogic_theme'): bool|string{
		$poFilePath = $this->buildPoFilePath($themeSlug);

		if(!$this->validatePoFile($poFilePath)){
			return false;
		}

		try{
			$this->translations = $this->loadTranslations($poFilePath);
			$this->processTranslations($this->translations);
			$this->saveTranslations($this->translations, $poFilePath);

			$this->log("✅ File updated: $poFilePath");
			return $poFilePath;

		}catch(\GPTTranslator\Exception $e){
			$this->log("❌ Error processing PO file: " . $e->getMessage());
			return false;
		}
	}

	/**
	 * Builds path to PO file
	 *
	 * @param string $themeSlug
	 *
	 * @return string
	 */
	private function buildPoFilePath(string $themeSlug): string{
		$contentDir = defined('WP_CONTENT_DIR')? WP_CONTENT_DIR : 'wp-content';
		return $contentDir . '/languages/loco/themes/' . $themeSlug . '-' . $this->toLocale . '.po';
	}

	/**
	 * Validates PO file existence and accessibility
	 *
	 * @param string $filePath
	 *
	 * @return bool
	 */
	private function validatePoFile(string $filePath): bool{
		if(!file_exists($filePath)){
			$this->log("❌ File does not exist: $filePath");
			return false;
		}

		if(!is_readable($filePath)){
			$this->log("❌ File is not readable: $filePath");
			return false;
		}

		if(!is_writable($filePath)){
			$this->log("❌ File is not writable: $filePath");
			return false;
		}

		return true;
	}

	/**
	 * Loads translations from PO file using gettext library
	 *
	 * @param string $filePath
	 *
	 * @return Translations
	 */
	private function loadTranslations(string $filePath): Translations{
		$loader = new PoLoader();
		return $loader->loadFile($filePath);
	}

	/**
	 * Processes translations - translates untranslated strings
	 *
	 * @param Translations $translations
	 */
	private function processTranslations(Translations $translations): void{
		// Update headers
		$this->updateHeaders($translations);

		foreach($translations as $translation){
			$this->processTranslation($translation);
		}
	}

	/**
	 * Updates PO file headers
	 *
	 * @param Translations $translations
	 */
	private function updateHeaders(Translations $translations): void{
		$currentDate = gmdate("Y-m-d H:i+0000");

		$headers = $translations->getHeaders();
		$headers->set('PO-Revision-Date', $currentDate);
		$headers->set('Last-Translator', 'GPT Translator');
		$translations->setLanguage($this->toLocale);
	}

	/**
	 * Processes individual translation
	 *
	 * @param Translation $translation
	 */
	private function processTranslation(Translation $translation): void{
		// Skip strings with "admin" or "untranslated" context
		if($this->shouldSkipTranslation($translation)){
			return;
		}

		// Skip already translated strings
		if($this->isAlreadyTranslated($translation)){
			return;
		}

		$this->translateEntry($translation);
	}

	/**
	 * Checks if translation should be skipped
	 *
	 * @param Translation $translation
	 *
	 * @return bool
	 */
	private function shouldSkipTranslation(Translation $translation): bool{
		$context = $translation->getContext();
		return in_array($context, ['admin', 'untranslated'], true);
	}

	/**
	 * Checks if string is already translated
	 *
	 * @param Translation $translation
	 *
	 * @return bool
	 */
	private function isAlreadyTranslated(Translation $translation): bool{
		//var_dump($translation->getPlural());
		if($translation->getPlural()){
			// For plural forms check all variants
			$plurals = $translation->getPluralTranslations();
			foreach($plurals as $plural){
				if(empty(trim($plural))){
					return false;
				}
			}
			return true;
		}else{
			// For regular strings
			return !empty(trim($translation->getTranslation()));
		}
	}

	/**
	 * Translates entry
	 *
	 * @param Translation $translation
	 */
	private function translateEntry(Translation $translation): void{
		if($translation->getPlural()){
			$this->translatePluralEntry($translation);
		}else{
			$this->translateSingularEntry($translation);
		}
	}

	/**
	 * Translates regular (non-plural) entry
	 *
	 * @param Translation $translation
	 */
	private function translateSingularEntry(Translation $translation): void{
		$original = $translation->getOriginal();

		if(empty(trim($original))){
			return;
		}

		$translated = $this->translator->translate($original);
		$translation->translate($translated);
	}

	/**
	 * Translates plural entry
	 *
	 * @param Translation $translation
	 */
	private function translatePluralEntry(Translation $translation): void{
		$singular = $translation->getOriginal();
		$plural = $translation->getPlural();

		if(empty(trim($singular)) || empty(trim($plural))){
			return;
		}

		// Get number of plural forms for target language
		$pluralCount = $this->getPluralCount();
		$declensionNumbers = $this->getDeclensionNumbers($pluralCount);

		$translations = [];

		for($i = 0; $i < $pluralCount; $i++){
			$number = $declensionNumbers[$i];
			$textTemplate = ($i === 0)? $singular : $plural;
			$exampleText = str_replace('%d', (string) $number, $textTemplate);

			$translated = $this->translator->translate($exampleText);

			// Replace number back to %d
			$translated = preg_replace('/\b' . preg_quote((string) $number, '/') . '\b/u', '%d', $translated);

			$translations[] = $translated;
		}

		// Set translations
		$translation->translate($translations[0]); // Singular
		if(count($translations) > 1){
			$translation->translatePlural(...array_slice($translations, 1)); // Plural forms
		}
	}

	/**
	 * Gets number of plural forms for target language
	 *
	 * @return int
	 */
	private function getPluralCount(): int{
		$headers = $this->translations->getHeaders();
		$pluralCount = 2;
		if(preg_match('/\s*nplurals\s*=\s*(\d+);/', $headers->get('Plural-Forms'), $match)){
			$pluralCount = (int) $match[1];
		}

		return $pluralCount;
	}

	/**
	 * Gets numbers for plural form declension
	 *
	 * @param int $pluralCount
	 *
	 * @return array
	 */
	private function getDeclensionNumbers(int $pluralCount): array{
		return array_map(function($i){
			return match ($i) {
				0 => 1,
				1 => 3,
				2 => 5,
				3 => 101,
				4 => 111,
				default => 999,
			};
		}, range(0, $pluralCount - 1));
	}

	/**
	 * Saves translations to PO file
	 *
	 * @param Translations $translations
	 * @param string       $filePath
	 *
	 * @throws \GPTTranslator\Exception
	 */
	private function saveTranslations(Translations $translations, string $filePath): void{
		$generator = new PoGenerator();
		$content = $generator->generateString($translations);

		if(file_put_contents($filePath, $content) === false){
			throw new \GPTTranslator\Exception("Failed to save translations to: $filePath");
		}
	}

	/**
	 * Logs message
	 *
	 * @param string $message
	 */
	private function log(string $message): void{
		call_user_func($this->logger, $message);
	}

}