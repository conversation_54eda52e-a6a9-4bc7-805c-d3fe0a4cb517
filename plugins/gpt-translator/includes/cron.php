<?php

if (!defined('ABSPATH')) {
	exit; // Запрет прямого доступа
}

require_once plugin_dir_path(__FILE__) . 'class-translator.php';


/*--------------------------------------------------------------
# Функции планирования и обработки Cron-задач
--------------------------------------------------------------*/

function custom_cron_interval( $schedules ) {
    $schedules['five_minutes'] = array(
        'interval' => 300,  // 5 минут в секундах
        'display'  => __( 'Every 5 Minutes' )
    );
    return $schedules;
}
add_filter( 'cron_schedules', 'custom_cron_interval' );








global $wpdb;

$queue_table = $wpdb->prefix . 'gpt_translator_translation_queue';
$chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

/**
 * Создаёт родительскую задачу и её чанки
 */
function gpt_translator_create_tasks($args) {
    global $wpdb, $queue_table, $chunk_table;


    $ids_to_translate = isset($args['ids_to_translate']) && is_array($args['ids_to_translate']) ? array_values($args['ids_to_translate']) : [];
    $ids_to_exclude   = isset($args['ids_to_exclude']) && is_array($args['ids_to_exclude']) ? array_values($args['ids_to_exclude']) : [];
    $post_type        = isset($args['post_type']) ? (array) $args['post_type'] : [];

    $success = $wpdb->insert($queue_table, [
        'ids_to_translate'          => wp_json_encode($ids_to_translate),
        'ids_to_translate_task'     => wp_json_encode($ids_to_translate),
        'ids_to_exclude'            => wp_json_encode($ids_to_exclude),
        'trans_status'              => sanitize_text_field($args['trans_status'] ?? ''),
        'status'                    => 'pending',
        'translate_posts'           => intval($args['translate_posts']),
        'overwrite_posts'           => intval($args['overwrite_posts']),
        'post_type'                 => wp_json_encode($post_type),
        'from_language'             => sanitize_text_field($args['from_language']),
        'to_language'               => sanitize_text_field($args['to_language']),
        'translate_po_file'         => intval($args['translate_po_file']),
        'translate_categories'      => intval($args['translate_categories']),
        'translate_menus'           => intval($args['translate_menus']),
        'translate_options'         => intval($args['translate_options']),
        'translate_user_desc'       => intval($args['translate_user_desc']),
        'translate_borlabs_dialog'  => intval($args['translate_borlabs_dialog']),
        'created_at'                => current_time('mysql'),
        'last_update'               => current_time('mysql'),
    ]);

    if (!$success) {
        print_r(date('Y-m-d H:i:s') . "❌ Task creation error: " . $wpdb->last_error . "\n");
        return false;
    }

    $parent_task_id = $wpdb->insert_id;

    if (!wp_next_scheduled('gpt_translator_watchdog')) {
        wp_schedule_event(time(), 'five_minutes', 'gpt_translator_watchdog');
    }
    



    if(intval($args['translate_categories'])){
       

        $wpdb->update($queue_table,
            ['status' => 'pre-translations-processing', 'last_update' => current_time('mysql')],
            ['id' => $parent_task_id]
        );

        createSingleChunk($parent_task_id,'translate_categories');
    } 


    // createChunks($parent_task_id, $ids_to_translate);

    // if($args['translate_po_file']){
    //     createSingleChunk($parent_task_id, ['translate_po_file']);
    // }
    // if($args['translate_menus']){
    //     createSingleChunk($parent_task_id, ['translate_menus']);
    // }
    // if($args['translate_options']){
    //     createSingleChunk($parent_task_id, ['translate_options']);
    // }
    // if($args['translate_user_desc']){
    //     createSingleChunk($parent_task_id, ['translate_user_desc']);
    // }
    // if($args['translate_borlabs_dialog']){
    //     createSingleChunk($parent_task_id, ['translate_borlabs_dialog']);
    // }

    

    gpt_translator_process_pending_chunks();
}



function createChunks($parent_task_id, $ids_to_translate) {
    global $wpdb;
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';
    $total_tasks = min(10, count($ids_to_translate));
    $chunks = array_fill(0, $total_tasks, []);

    // Распределяем ID по задачам
    for ($i = 0; $i < $total_tasks; $i++) {
        $chunks[$i] = [];
    }

    foreach ($ids_to_translate as $index => $id) {
        $chunk_index = $index % $total_tasks;
        $chunks[$chunk_index][] = $id;
    }

    foreach ($chunks as $chunk) {
        if (!empty($chunk)) {
            createSingleChunk($parent_task_id, $chunk);
        }
    }

}

function createSingleChunk($parent_task_id, $ids_to_translate) {
    global $wpdb;
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

    print_r("createSingleChunk:\n");
    print_r($ids_to_translate);
    print_r("\n");

    $result = $wpdb->insert($chunk_table, [
        'parent_task_id'   => $parent_task_id,
        'ids_to_translate' => wp_json_encode($ids_to_translate),
        'status'           => 'pending',
        'created_at'       => current_time('mysql'),
        'last_update'      => current_time('mysql'),
    ]);

    if ($result === false) {
        print_r(date('Y-m-d H:i:s') . "❌ Failed to insert chunk record\n");
        return false;
    }

    return $wpdb->insert_id;
}

function gpt_translator_process_pending_chunks() {
    global $wpdb;
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';

  
    $pending_chunks = $wpdb->get_results("
        SELECT id FROM $chunk_table 
        WHERE status = 'pending' 
            OR (status = 'processing' AND last_update < NOW() - INTERVAL 3 MINUTE)
        ORDER BY created_at ASC 
        LIMIT 15
    ");

    if (empty($pending_chunks)) {
        return;
    }

    $plugin_dir = plugin_dir_path(__FILE__);
    $logs_dir = $plugin_dir . 'logs/';  
    $log_file = $logs_dir . 'command.log';

   
    if (!file_exists($logs_dir)) {
        wp_mkdir_p($logs_dir);
    }

    if (!file_exists($log_file)) {
        file_put_contents($log_file, "=== GPT Translator Command Log ===\n");
        chmod($log_file, 0644); // set correct permissions
    }


    $wp_cli_path = '/opt/bitnami/wp-cli/bin/wp'; 

    foreach ($pending_chunks as $chunk) {
        $cmd = sprintf(
            '%s gpt_translator:process_chunk %d',
            escapeshellcmd($wp_cli_path),
            (int)$chunk->id
        );
        
        file_put_contents($log_file, date('Y-m-d H:i:s') . " Executing: $cmd\n", FILE_APPEND);
        
        exec("$cmd >> $log_file 2>&1 &", $output, $return_var);
        
        if ($return_var !== 0) {
            file_put_contents($log_file, date('Y-m-d H:i:s') . " ❌ Failed to start command\n", FILE_APPEND);
        }
    }
}

add_action('gpt_translator_watchdog', 'gpt_translator_process_pending_chunks');





function processChunk($chunk_id) {
    
    // SimpleOpenAITranslator::createLogRecord("Processing chunk $chunk_id");

    global $wpdb;
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';
    $queue_table = $wpdb->prefix . 'gpt_translator_translation_queue';
    

    // Получаем данные чанка
    $chunk_data = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $chunk_table WHERE id = %d", $chunk_id),
        ARRAY_A
    );

    
    if (!$chunk_data) {
        print_r(date('Y-m-d H:i:s') . "❌ Chunk data not found for chunk $chunk_id\n");
        return;
    }
    
    $parent_task_id = $chunk_data['parent_task_id'];
    $chunk_status = $chunk_data['status'];
    $chunk_task = $chunk_data['ids_to_translate'];

    $parent_task = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $queue_table WHERE id = %d", $parent_task_id),
        ARRAY_A
    );
    
    $parent_status = $parent_task['status'];

    if (!$parent_task) {
        $wpdb->update($chunk_table, ['status' => 'error'], ['id' => $chunk_id]);
        print_r(date('Y-m-d H:i:s') . "❌ Parent task not found for chunk $chunk_id\n");
        return;
    }

   

    // set status 'processing'
    if($chunk_status == 'pending') { 
       
        try {
            $updated = $wpdb->query(
                $wpdb->prepare(
                    "UPDATE $chunk_table 
                    SET status = 'processing', last_update = NOW() 
                    WHERE id = %d AND status = 'pending'",
                    $chunk_id
                )
            );
            
            // if ($updated === false) {
            //     print_r(date('Y-m-d H:i:s') . "❌ Failed to lock chunk $chunk_id\n");
            //     return;
            // }
            
            // if ($updated === 0) {
            //     print_r(date('Y-m-d H:i:s') . "Chunk $chunk_id already processed\n");
            //     return;
            // }   

           
            //  $updated_parent = $wpdb->query(
            //     $wpdb->prepare(
            //         "UPDATE $queue_table 
            //          SET status = 'processing' 
            //          WHERE id = %d AND status = 'pending'",
            //         $parent_task_id
            //     )
            // );
    
            // if ($updated_parent === false) {
            //     print_r(date('Y-m-d H:i:s') . "❌ Failed to update parent task $parent_task_id\n");
            //     return;
            // }

        } catch (Exception $e) {
            print_r(date('Y-m-d H:i:s') . "❌ Transaction failed: " . $e->getMessage() . "\n");
            return;
        }
    }

    $translator = new SimpleOpenAITranslator(
        $parent_task['to_language'],
        $parent_task['from_language']
    );

    print_r("Chunk task: " . $chunk_task . "\n");


    if($chunk_task === "translate_categories"){
        print_r("🚀 Starting categories creation\n");
        $translator->translate_categories();
    }

    if($parent_status == 'pre-translations-processing'){
        SimpleOpenAITranslator::createLogRecord("🚀 Starting categories creation");
        print_r("🚀 Starting categories creation\n");
        $translator->$chunk_task();

        $wpdb->update($queue_table, 
            ['status' => 'processing-posts', 'last_update' => current_time('mysql')], 
            ['id' => $parent_task_id]
        );

        gpt_translator_process_pending_chunks();

        SimpleOpenAITranslator::createLogRecord("✅ Categories tasks completed");
        print_r("✅ Categories tasks completed\n");
    }
    elseif($parent_status == 'processing-posts'){
        
        $wpdb->update($queue_table, 
            ['status' => 'post-translations-processing', 'last_update' => current_time('mysql')], 
            ['id' => $parent_task_id]
        );

        gpt_translator_process_pending_chunks();
    }
    else{

    }

/////////////////////
    // print_r("Processing chunk task:\n");
    // print_r($chunk_task);
    // print_r("\n");

    sleep(5);

////////////////////

       
    $wpdb->update($chunk_table, 
        ['status' => 'completed', 'last_update' => current_time('mysql')], 
        ['id' => $chunk_id]
    );




    $remaining_chunks = $wpdb->get_var(
        $wpdb->prepare(
            "SELECT COUNT(*) FROM $chunk_table 
            WHERE parent_task_id = %d AND status NOT IN ('completed', 'error')",
            $parent_task_id
        )
    );

    print_r("Remaining chunks: " . $remaining_chunks . "\n");
        
    if ($remaining_chunks == 0) {

        //runAfterPostsTranslateActions($parent_task_id);

        $wpdb->update($queue_table, 
            ['status' => 'completed', 'last_update' => current_time('mysql')], 
            ['id' => $parent_task_id]
        );

        print_r("Parent task completed: " . $parent_task_id . "\n");
    }

   

    // try {
    //     $translator = new SimpleOpenAITranslator(
    //         $parent_task['to_language'],
    //         $parent_task['from_language']
    //     );
        
    //     if ($processing_pre_translate) {
    //         SimpleOpenAITranslator::createLogRecord("🚀 Starting categories creation");
    //         $translator->createTranslateCategories();
    //         SimpleOpenAITranslator::createLogRecord("✅ Categories tasks completed");

    //     } 
    //     else {

           
    //         $post_ids = json_decode($chunk_data['ids_to_translate'], true) ?: [];
    //         $post_ids_excluded = json_decode($parent_task['ids_to_exclude'], true) ?: [];
    //         $post_types = json_decode($parent_task['post_type'], true) ?: [];
            
    //         //SimpleOpenAITranslator::createLogRecord("🚀 Processing posts translation for chunk $chunk_id");
            
    //         $translator->doTranslations(
    //             $post_ids,
    //             $post_ids_excluded,
    //             $parent_task['trans_status'],
    //             $parent_task['translate_posts'],
    //             $parent_task['overwrite_posts'],
    //             $post_types,
    //             $chunk_id,
    //             $parent_task_id
    //         );
    //     }


    //     // Обновляем статус чанка
    //     $wpdb->update($chunk_table, 
    //         ['status' => 'completed', 'last_update' => current_time('mysql')], 
    //         ['id' => $chunk_id]
    //     );
    
    //     $parent_ids_to_translate = json_decode($parent_task['ids_to_translate'], true);

    //     if($processing_pre_translate && !empty($parent_ids_to_translate)){
           
    //         try {

    //             $total_tasks = min(10, count($parent_ids_to_translate));
    //             $chunks = [];

    //             // Распределяем ID по задачам
    //             for ($i = 0; $i < $total_tasks; $i++) {
    //                 $chunks[$i] = [];
    //             }

    //             foreach ($parent_ids_to_translate as $index => $id) {
    //                 $chunk_index = $index % $total_tasks;
    //                 $chunks[$chunk_index][] = $id;
    //             }

    //             foreach ($chunks as $chunk) {
    //                 if (!empty($chunk)) {
    //                     $wpdb->insert($chunk_table, [
    //                         'parent_task_id'   => $parent_task_id,
    //                         'ids_to_translate' => wp_json_encode($chunk),
    //                         'status'           => 'pending',
    //                         'created_at'       => current_time('mysql'),
    //                     ]);
    //                 }
    //             }

    //             $total_chunks = count($chunks);
    //             gpt_translator_process_pending_chunks();
               
    //         } catch (Exception $e) {
    //             print_r(date('Y-m-d H:i:s') . "❌ Error updating parent task: " . $e->getMessage() . "\n");
    //             return;
    //         }
    //     }




    //     // Проверяем завершение всей задачи
    //     $remaining_chunks = $wpdb->get_var(
    //         $wpdb->prepare(
    //             "SELECT COUNT(*) FROM $chunk_table 
    //             WHERE parent_task_id = %d AND status NOT IN ('completed', 'error')",
    //             $parent_task_id
    //         )
    //     );
        
    //     if ($remaining_chunks == 0) {

    //         runAfterPostsTranslateActions($parent_task_id);

    //         $wpdb->update($queue_table, 
    //             ['status' => 'completed', 'last_update' => current_time('mysql')], 
    //             ['id' => $parent_task_id]
    //         );
    //     }
        
    // } catch (Exception $e) {
    //     $wpdb->update($chunk_table, ['status' => 'error'], ['id' => $chunk_id]);
    //     $wpdb->update($queue_table, ['status' => 'error'], ['id' => $parent_task_id]);
    //     print_r(date('Y-m-d H:i:s') . "❌ Error processing chunk $chunk_id: " . $e->getMessage() . "\n");
    //     return;
    // }
}



function runAfterPostsTranslateActions($parent_task_id) {
    global $wpdb;

    sleep(5);


    $queue_table = $wpdb->prefix . 'gpt_translator_translation_queue';
    $chunk_table = $wpdb->prefix . 'gpt_translator_translation_chunks';
    
    $parent_task = $wpdb->get_row(
        $wpdb->prepare("SELECT * FROM $queue_table WHERE id = %d", $parent_task_id),
        ARRAY_A
    );
    
    if (!$parent_task) {
        print_r(date('Y-m-d H:i:s') . "❌ Parent task not found for post-translate actions\n");
        return;
    }

    $translator = new SimpleOpenAITranslator(
        $parent_task['to_language'],
        $parent_task['from_language']
    );

    // Передаем пустой массив как второй параметр, если у вас нет конкретных ID для перевода
    $chunk_id = createSingleChunk($parent_task_id);

    if (!$chunk_id) {
        print_r(date('Y-m-d H:i:s') . "❌ Failed to create chunk\n");
        return;
    }

    try {
      
        $translator->runPostTranslateSteps(
            $parent_task['translate_po_file'],
            $parent_task['translate_menus'],
            $parent_task['translate_options'],
            $parent_task['translate_user_desc'],
            $parent_task['translate_borlabs_dialog'],
            $parent_task_id
        );

        $updated = $wpdb->update($chunk_table, 
            ['status' => 'completed', 'last_update' => current_time('mysql')], 
            ['id' => $chunk_id]
        );

        if ($updated === false) {
            print_r(date('Y-m-d H:i:s') . "❌ Failed to update chunk status\n");
            return;
        }

        sleep(5);
        print_r(date('Y-m-d H:i:s') . " ✅ Post-translate actions completed\n");
    } catch (Exception $e) {
        print_r(date('Y-m-d H:i:s') . " ❌ Post-translate error: " . $e->getMessage() . "\n");
        return;
    }
}

