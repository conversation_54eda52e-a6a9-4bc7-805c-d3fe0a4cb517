<?php

add_action('add_meta_boxes', function () {
    global $post;

    // Check if source_post_ID exists and is not empty
    if (!$post || !get_post_meta($post->ID, 'source_post_ID', true)) {
        return; // Don't add meta box if source_post_ID is empty
    }

    $post_types = ['post', 'page'];

    foreach ($post_types as $post_type) {
        add_meta_box(
            'gpt_translation_meta_box',
            'GPT Translated',
            'render_gpt_translation_meta_box',
            $post_type,
            'side',
            'high'
        );
    }
});

function render_gpt_translation_meta_box($post) {
    global $sitepress;

    $languages = $sitepress->get_active_languages();

    $source_id = get_post_meta($post->ID, 'source_post_ID', true);
    $source_posts_url = get_permalink($source_id);
    $source_posts_title = get_the_title($source_id);
    $from_lang = get_post_meta($post->ID, 'from_lang', true);
    $to_lang   = get_post_meta($post->ID, 'to_lang', true);


    echo '<p><strong>Source post:</strong><br>';
    if ( $source_id && $source_posts_title ) {
        echo '<a href="' . esc_url($source_posts_url) . '" target="_blank">' . esc_html($source_posts_title) . '</a>';
    } else {
        echo 'Not found';
    }
    echo '</p>';

    if ( isset($languages[$from_lang]) && isset($languages[$to_lang]) ) {
        $from_flag = $sitepress->get_flag_url( $from_lang );
        $to_flag = $sitepress->get_flag_url( $to_lang );

        echo '<p><strong>Translation:</strong><br>';
        echo '<img src="' . esc_url($from_flag) . '" alt="' . esc_attr($languages[$from_lang]['english_name']) . '" width="20" height="15"> ';
        echo esc_html($languages[$from_lang]['english_name']) . ' → ';
        echo '<img src="' . esc_url($to_flag) . '" alt="' . esc_attr($languages[$to_lang]['english_name']) . '" width="20" height="15"> ';
        echo esc_html($languages[$to_lang]['english_name']);
        echo '</p>';
    }
}
