<?php

add_action('add_meta_boxes', function () {
    add_meta_box(
        'gpt_translation_meta_box',            // ID
        'GPT Translation Info',                // Заголовок
        'render_gpt_translation_meta_box',     // Callback
        'post',                                // Тип записи (можно заменить на ваш кастомный)
        'side',                                // Место отображения
        'default'                              // Приоритет
    );
});

function render_gpt_translation_meta_box($post) {
    $source_id = get_post_meta($post->ID, 'source_post_ID', true);
    $from_lang = get_post_meta($post->ID, 'from_lang', true);
    $to_lang   = get_post_meta($post->ID, 'to_lang', true);

    echo '<p><strong>Source Post ID:</strong> ' . esc_html($source_id ?: '-') . '</p>';
    echo '<p><strong>From Lang:</strong> ' . esc_html($from_lang ?: '-') . '</p>';
    echo '<p><strong>To Lang:</strong> ' . esc_html($to_lang ?: '-') . '</p>';
}
