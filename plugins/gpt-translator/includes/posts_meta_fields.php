<?php

add_action('add_meta_boxes', function () {
    add_meta_box(
        'gpt_translation_meta_box',            // ID
        'GPT Translated',                // Заголовок
        'render_gpt_translation_meta_box',     // Callback
        'post',                                // Тип записи (можно заменить на ваш кастомный)
        'side',                                // Место отображения
        'high'                              // Приоритет
    );

    // Добавляем мета-бокс для страниц
    add_meta_box(
        'gpt_translation_meta_box',            // ID
        'GPT Translated',                // Заголовок
        'render_gpt_translation_meta_box',     // Callback
        'page',                                // Тип записи - страницы
        'side',                                // Место отображения
        'high'                              // Приоритет
    );
});

function render_gpt_translation_meta_box($post) {
    $source_id = get_post_meta($post->ID, 'source_post_ID', true);
    $source_posts_url = get_permalink($source_id);
    $source_posts_title = get_the_title($source_id);
    $from_lang = get_post_meta($post->ID, 'from_lang', true);
    $to_lang   = get_post_meta($post->ID, 'to_lang', true);

    echo '<p>Source post: <a href="' . $source_posts_url . '" target="_blank">' . $source_posts_title . '</a></p>';
    echo '<p>From Lang: ' . $from_lang ?: '-' . '</p>';
    echo '<p>To Lang: ' . $to_lang ?: '-' . '</p>';
}