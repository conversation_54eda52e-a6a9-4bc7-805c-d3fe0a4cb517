(()=>{"use strict";const e=window.React,l=window.wp.plugins,a=window.wp.editPost,t=window.wp.components,n=window.wp.data,i=window.wp.coreData,o=window.wp.primitives,r=(0,e.createElement)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},(0,e.createElement)(o.Path,{fillRule:"evenodd",d:"M8.95 11.25H4v1.5h4.95v4.5H13V18c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2v-3c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v.75h-2.55v-7.5H13V9c0 1.1.9 2 2 2h3c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2h-3c-1.1 0-2 .9-2 2v.75H8.95v4.5ZM14.5 15v3c0 .*******.5h3c.3 0 .5-.2.5-.5v-3c0-.3-.2-.5-.5-.5h-3c-.3 0-.5.2-.5.5Zm0-6V6c0-.3.2-.5.5-.5h3c.3 0 .5.2.5.5v3c0 .3-.2.5-.5.5h-3c-.3 0-.5-.2-.5-.5Z",clipRule:"evenodd"})),u=()=>{const l=(0,n.useSelect)((e=>e("core/editor").getCurrentPostType()),[]),[a,o]=(0,i.useEntityProp)("postType",l,"meta"),r=(e,l)=>{const t=document.getElementById(e);t&&("checkbox"===t.type?t.checked=l:t.value=l),o({...a,[e]:l})};return(0,e.createElement)(t.PanelBody,null,(0,e.createElement)(t.ToggleControl,{label:"Exclude from Sitemap",checked:a?._sitemap_exclude,onChange:e=>r("_sitemap_exclude",e)}),(0,e.createElement)("br",null),(0,e.createElement)(t.SelectControl,{label:"Post Priority",value:a?._sitemap_priority,onChange:e=>r("_sitemap_priority",e),options:[{label:"Default",value:""},{label:"0.0",value:"0"},{label:"0.1",value:"1"},{label:"0.2",value:"2"},{label:"0.3",value:"3"},{label:"0.4",value:"4"},{label:"0.5",value:"5"},{label:"0.6",value:"6"},{label:"0.7",value:"7"},{label:"0.8",value:"8"},{label:"0.9",value:"9"},{label:"1.0",value:"10"}]}),(0,e.createElement)("br",null),(0,e.createElement)(t.SelectControl,{label:"Post Frequency",value:a?._sitemap_frequency,onChange:e=>r("_sitemap_frequency",e),options:[{label:"Default",value:""},{label:"Always",value:"always"},{label:"Hourly",value:"hourly"},{label:"Daily",value:"daily"},{label:"Weekly",value:"weekly"},{label:"Monthly",value:"monthly"},{label:"Yearly",value:"yearly"},{label:"Never",value:"never"}]}),"undefined"!=typeof sitemapSettings&&!sitemapSettings?.isProEnabled&&(0,e.createElement)("div",{className:"pro-overlay gutenberg"},(0,e.createElement)("div",null,"This feature is available on Premium version",(0,e.createElement)("a",{href:"https://wpgrim.com/google-xml-sitemaps-generator-pro/?utm_source=sgg-plugin&utm_medium=meta-box-gutenberg&utm_campaign=xml_sitemap",target:"_blank"},"Get Now"))))};(0,l.registerPlugin)("sitemap-settings",{render:()=>(0,e.createElement)(a.PluginSidebar,{name:"sitemap-settings",title:"XML Sitemaps",icon:r},(0,e.createElement)(u,null))})})();