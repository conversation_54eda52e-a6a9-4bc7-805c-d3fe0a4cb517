=== Dynamic XML Sitemaps Generator for Google ===
Contributors: wpgrim
Donate link: https://wpgrim.com/google-xml-sitemaps-generator-pro/?utm_source=wordpressorg&utm_medium=donate&utm_campaign=xml_sitemap
Tags: sitemap, xml sitemap, google news, image sitemap, video sitemap, google sitemaps, xml, google, seo, sitemaps
Requires at least: 5.0
Requires PHP: 5.6
Tested up to: 6.8.1
Stable tag: 2.1.11
License: GPLv2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html

Greatly improves your site's SEO 🚀 with special XML Sitemaps, HTML Sitemap, Image Sitemap, Video Sitemap and Google News.

== Description ==

## XML Sitemaps

Google XML Sitemaps Generator is a WordPress Sitemap plugin that improves your website's SEO by creating special XML Sitemaps for Search Engines like Google, Yahoo, Bing, Ask.com, and Yandex to index your site faster.

This plugin generates detailed **XML Sitemaps** that include your website's Pages, Posts, Custom Posts, and Taxonomies, enhancing your website's Search Engine Optimization (SEO) score. Plugin offers following Sitemaps:

* **General Sitemap** - single XML Sitemap with all links
* **Sitemap Index** - multiple Sitemaps in a single Sitemap Index
* **Google News Sitemap**
* **Image Sitemap**
* **Video Sitemap**
* **Multilingual Sitemap** - WPML, Polylang, TranslatePress compatible

⭐ [Documentation](https://wpgrim.com/docs/google-xml-sitemaps-generator/general/settings/?utm_source=wordpressorg&utm_medium=documentation&utm_campaign=xml_sitemap)

## Sitemap Structure

The plugin offers two primary XML Sitemap structures: **Single Sitemap** and **Sitemap Index**. The Single Sitemap displays all your website's content in a single XML Sitemap, while the Sitemap Index shows multiple XML Sitemaps in a single Sitemap Index.

**Sitemap Index** feature enables you to manage your sitemaps with **Multiple Inner Sitemaps** for various content types such as Pages, Posts, Custom Posts, Images, Videos and more. This feature makes easy the management of your Sitemaps and keep them organized.

## Google News

Make your website's News easily discoverable by Search Engines with Google News feature. By enabling "Google News" in the settings, a dynamic [Google News Sitemap](https://developers.google.com/search/docs/crawling-indexing/sitemaps/news-sitemap) will be created automatically. Google News Sitemap is fully compatible with popular multilingual plugins like **WPML**, **Polylang**, and **TranslatePress**.

The Google News Sitemap includes all the required fields such as **Publication Name**, **Language**, **Title**, and **Publication Date**. Additionally, it is fully compatible with the Google Publisher Center sitemap protocol, ensuring that your website's content is properly indexed and displayed in Google News.

## Image Sitemap

The Dynamic Image Sitemap is a valuable tool for optimizing website indexing and search engine visibility. By including images from Pages, Posts, and Custom Posts Content, it offers a comprehensive representation of visual content on your site. Additionally, its adherence to the [Google Image Sitemaps](https://developers.google.com/search/docs/crawling-indexing/sitemaps/image-sitemaps) structure ensures compatibility and optimal performance within the search engine ecosystem.

## Video Sitemap

Video Sitemap will also be dynamically generated using collected Videos from Pages, Posts, and Custom Posts Content that contains Playlist Shortcode or <video> tag nd fully compatible with [Google Video Sitemaps](https://developers.google.com/search/docs/crawling-indexing/sitemaps/video-sitemaps) structure. Title, Thumbnail, Description, and Duration fields will be respectively generated from Video Attachment's Title, Featured Image, Caption, and Length.

## Custom Sitemap URLs

Customizing your XML Sitemap is ease with Custom Sitemap URLs feature. You can add **External or Internal URLs** to your XML Sitemap under the Plugin Settings, making it easy to customize your XML Sitemap to fit your specific needs. Also, you can add **Custom Sitemaps** to the Sitemap Index, if you have Static or Additional Sitemaps.

In addition, Google XML Sitemaps Generator plugin allows you to display a Sitemap under a **Custom URL** on your website, providing an additional level of customization.

## Sitemap Options

This plugin provides you with a very simple and clean UI to set **Priority**, **Frequency** options and easily **Include/Exclude** Pages, Posts, Custom Posts, Archives, Authors, Categories, Taxonomies and Tags into your Sitemap.

## Cache

The WordPress XML Sitemap plugin comes equipped with a Cache feature that automatically caches Sitemap Data when a user visits the Sitemap page. This means that subsequent visits to the sitemap page will be faster as the cached data is quickly retrieved instead of generating the Sitemap from scratch.

Cache feature also includes options to set a **Caching Timeout**, view the **Last Cached Time**, and **Clear Cache** when necessary. These features help to improve the performance of your website and ensure that your Sitemap is always up-to-date for Search Engines to crawl.

Here is a short list of Google XML Sitemap plugin features.

**Features:**

* Multiple Sitemaps by Sitemap Index 🔥
* Custom XML Sitemap URL 🔥
* IndexNow Protocol for indexing Microsoft Bing, Seznam.cz, Naver, and Yandex
* Control Sitemap Options for all Pages, Posts, Custom Posts, Archives, Authors, Categories, Taxonomies and Tags 🔥
* Include/Exclude Taxonomies, Categories, Tags 🔥
* Add Custom Sitemaps to Sitemap Index
* Add Custom External/Internal URLs to your XML Sitemap 🔥
* Automatically add Sitemaps to robots.txt
* Links Per Page for Sitemap Index 🔥
* Static Sitemap File detector
* Toolbar with most helpful Actions & Links
* Disables auto-generated WP Sitemaps
* Setup Wizard 🔥
* WP-CLI commands for Sitemap generation **[NEW]** 🔥
* Multisite Network compatible
* WooCommerce compatible
* Multilingual Sitemap 🔥
* WPML compatible
* Polylang compatible
* TranslatePress compatible
* FooGallery compatible
* Yoast SEO compatible with noindex
* Rank Math SEO compatible with noindex
* SEO Framework compatible
* User-Friendly Interface
* No Coding required

**Google News Features:**

* Sitemap Tags: Publication Name, Language, Title and Publication Date
* Custom Google News URL 🔥
* Custom News Publication Name
* WPML compatible
* Polylang compatible
* TranslatePress compatible

**Image Sitemap Features**

* Hide Image Previews **[NEW]**
* Multiple Sitemaps by Sitemap Index
* Include Pages & Posts
* Custom Image Sitemaps URLs

**Video Sitemap Features**

* Multiple Sitemaps by Sitemap Index
* Include Pages & Posts
* Custom Video Sitemaps URLs

**Advanced Features**

* Cache Sitemaps 🔥
* Cache Expiration Time
* Last Cached Time of Sitemaps
* Import & Export Settings

## Upgrade to Pro Version

Upgrade to the [Google XML Sitemaps Generator Pro](https://wpgrim.com/google-xml-sitemaps-generator-pro/?utm_source=wordpressorg&utm_medium=content&utm_campaign=xml_sitemap) to get access to even more features, like an HTML Sitemap, Exclusion of specific Pages and Custom Posts from your Sitemap, Google News Premium Features, and more.

[Get Pro Version now](https://wpgrim.com/google-xml-sitemaps-generator-pro/?utm_source=wordpressorg&utm_medium=get-pro-version&utm_campaign=xml_sitemap) and take your Search Engine Optimization to the Next Level.

**Custom Priority and Frequency for a Single Page/Post**

Customize the **Priority**, **Frequency**, and **Exclude from Sitemap** options for a Single Post, Page, or CPT [with the Pro Version](https://wpgrim.com/google-xml-sitemaps-generator-pro/?utm_source=wordpressorg&utm_medium=post-meta&utm_campaign=xml_sitemap).

**HTML Sitemap**

Improve your website's navigation with HTML Sitemap feature. This feature allows you to create an HTML Sitemap that can be displayed on your website's pages using popular Page Builder Widgets such as Elementor, Gutenberg, WPBakery, or simple Shortcode.

You can also customize your HTML Sitemap by choosing which Post Types to display, including Pages, Posts, and Custom Post Types, through the convenient multiselect option. With this feature, you can easily enhance your website's user experience and make it more SEO-friendly. [Try Pro Version](https://wpgrim.com/google-xml-sitemaps-generator-pro/?utm_source=wordpressorg&utm_medium=html-sitemap&utm_campaign=xml_sitemap) today!

**Premium Features**

* Exclude Pages, Posts, Custom Posts from Sitemap
* Exclude Categories and Tags from Sitemap 🔥
* Include only selected Categories & Tags **[NEW]**
* HTML Sitemap 🔥
* Single Post/Page Custom Sitemap Options: 🔥
-- Exclude from Sitemap
-- Post Priority
-- Post Frequency
* Posts Priority Automatic Calculation:
-- by Number of Comments
-- by Average Comments Count
* Page Builder Widgets and Shortcode for **HTML Sitemap**:
-- Elementor widget
-- Gutenberg block
-- WPBakery (Visual Composer) widget
-- **[html-sitemap post-types="page,post,.." ..]**
* Sitemap Custom Colors **[NEW]** 🔥
* No Branding Marks 🔥
* Premium Support

**Google News Premium Features**

* Include Custom Post Types
* Include Taxonomies
* Keywords 🔥
* Stock Tickers 🔥
* Exclude Posts and Custom Posts
* Exclude Categories and Tags 🔥
* Include only selected Categories & Tags **[NEW]**

**Image Sitemap Premium Features**

* Image Sitemap MIME Types
* Exclude Broken (404) Images 🔥
* Include Featured Images
* Include WooCommerce Gallery 🔥
* Include Custom Post Types

**Video Sitemap Premium Features**

* YouTube videos 🔥
* Vimeo videos **[NEW]** 🔥
* X (Twitter) videos **[NEW]** 🔥
* Include Custom Post Types

**Advanced Premium Features**

* Smart Caching 🔥
* Minimize Sitemap source code
* Cron Job **[NEW]** 🔥

⭐ [Get PRO Version Now](https://wpgrim.com/google-xml-sitemaps-generator-pro/?utm_source=wordpressorg&utm_medium=get_pro&utm_campaign=xml_sitemap)

## More Powerful Plugins

[Classic Editor and Classic Widgets](https://wordpress.org/plugins/classic-editor-and-classic-widgets/)

Developed with ❤️ by [WP Grim](https://wpgrim.com/?utm_source=wordpressorg&utm_medium=direct&utm_campaign=xml_sitemap)

== Screenshots ==

1. General Settings
2. Sitemap Options
3. Google News Settings
4. Media Sitemaps Settings
5. Advanced Settings
6. XML Sitemap
7. Sitemap Index
8. Video Sitemap
9. Image Sitemap
10. Google News
11. Multilingual Sitemap
12. Setup Wizard

== Frequently Asked Questions ==

= Where can I find Plugin Settings? =

You can find plugin options under **Settings > XML Sitemaps**.

= How to fix Sitemap 404 Not Found error? =

First, try to flush Rewrite Rules. You can find **Flush Rewrite Rules** button under **Settings > XML Sitemaps > Tools** sidebar. 

If you are using NGINX webserver, you may need to add extra Rewrite Rules to your NGINX configuration file. You can find the NGINX configuration in the **Webserver Configuration** section under **Settings > XML Sitemaps**.

= How to submit Sitemap URL to Google Webmaster Tools? =

1. Sign in to Google [Search Console](https://search.google.com/search-console).

2. Select your website in the Sidebar and click on **Sitemaps** menu.

3. Enter **https://yoursite.com/sitemap.xml** in "Add a new sitemap" field and click **Submit**.

= Can I modify Priority and Frequency options? =

You can find Sitemap Content Options under **Settings > XML Sitemaps > XML Sitemap**.

= How to publish Posts on Google News? =

Follow this guide in order to [Show up your Publications on Google News](https://support.google.com/news/publisher-center/answer/9607025?hl=en). Also, you need to [Create a Google News section](https://support.google.com/news/publisher-center/answer/9548609?hl=en) from part of your website.

You need also to add the Sitemap URL to your [Google Search Console](https://search.google.com/search-console) account for validation.

= How to show HTML Sitemap on my Pages? =

You can show HTML Sitemap on your Pages using popular Page Builder Widgets such as **Elementor**, **Gutenberg**, **WPBakery (Visual Composer)**, or simple Shortcode like:

**[html-sitemap post-types="page,post,.." show-featured-image="true" show-date="true" date-format="F j, Y"]**

= How to set custom Priority and Frequency for chosen Page, Post, or Term? =

You can set custom Priority and Frequency for chosen Page, Post, or Term using filters:

**sitemap_post_priority**, **sitemap_post_frequency**, **sitemap_term_priority**, **sitemap_term_frequency**

Example:

``
add_filter( 'sitemap_post_priority', 'my_sitemap_post_priority', 10, 2 );
function my_sitemap_post_priority( $priority, $post_id ) {
    if ( $post_id === 1 ) {
        $priority = 9; // priority from 0 to 10
    }
    return $priority;
}
``

= How to generate Sitemap using CLI? =

You can generate Sitemap using CLI command:

`wp sitemap generate`

You can generate a specific Sitemap type:

`wp sitemap generate --template=image-sitemap`

Allowed Sitemap types are **sitemap**, **image-sitemap**, **video-sitemap**, and **google-news**. Default template is **sitemap**.

== Installation ==
This section describes how to install the plugin and get it working.

1. Upload the plugin files to the `/wp-content/plugins/` directory, or install the plugin through the WordPress plugins screen directly.
2. Activate the plugin through the **Plugins** menu in WordPress
3. Please find more details on [Plugin Documentation](https://wpgrim.com/docs/google-xml-sitemaps-generator/general/settings/?utm_source=wordpressorg&utm_medium=installation&utm_campaign=xml_sitemap)
4. Set Up Page in **Settings -> XML Sitemaps**.

== Changelog ==

= 2.1.11 - 2025-07-05 =
* Troubleshoot section added to Settings page
* WP CLI instructions section added to Settings page
* Pro Version: Cron Job feature added
* **sgg_sitemap_exclude_post_ids** and **sgg_sitemap_exclude_term_ids** filters added for customizing Exclude Post and Term IDs
* **sgg_sitemap_include_only_term_ids** filter added for customizing Include only Term IDs

= 2.1.10 - 2025-06-17 =
* Media Sitemap: BeeTheme compatibility added, as it uses post_meta for storing content
* Media Sitemap: Post Parent URL issue fixed
* Media Sitemap: Noindex Posts excluded from Image & Video Sitemaps

= 2.1.9 - 2025-05-22 =
* Sitemap Generator CLI command added
* **template_redirect** action replaced with **parse_request** action for early execution of Sitemaps

= 2.1.8 - 2025-05-18 =
* Old media_sitemap Content checks removed

= 2.1.7 - 2025-05-16 =
* Creation of dynamic property is deprecated PHP error fixed. Thanks to @track77 and @btreece84

= 2.1.5 - 2025-05-15 =
* SEO Framework noindex Pages, Posts, and Custom Posts excluded from Sitemap
* SEO Framework noindex Categories and Taxonomies excluded from Sitemap
* Image Sitemap: AVIF image support added
* Google News: SEO Framework custom title support added
* Image Sitemap & Video Sitemap settings separatedy
* WordPress 6.8.1 compatible

= 2.1.4 - 2025-04-24 =
* Google News: **xml_sitemap_google_news_title** filter added for customizing Title
* Child Posts URL issue fixed

= 2.1.3 - 2025-04-15 =
* WordPress 6.8 compatible

= 2.1.2 - 2025-03-18 =
* Caching Chunk Size increased

= 2.1.1 - 2025-03-18 =
* Minor bug fixes

= 2.1.0 - 2025-03-14 =
* Image Sitemap: Multiple Sitemaps (Per Paged) by Sitemap Index feature added
* Image Sitemap: Hide Image Previews feature added
* Video Sitemap: Multiple Sitemaps (Per Paged) by Sitemap Index feature added
* Media Sitemaps: Database Queries optimized
* Media Sitemaps: Smart Caching feature implemented for faster loading
* **xml_sitemap_disable_post_meta_fields** filter added for disabling Post Meta Fields
* **xml_sitemap_disable_post_meta__exclude_sitemap**, **xml_sitemap_disable_post_meta__priority**, **xml_sitemap_disable_post_meta__frequency** filters added for disabling Post Meta Fields

= 2.0.8 - 2025-03-02 =
* Pro Version: XML Sitemaps > Include only selected Categories & Tags feature added
* Pro Version: Google News > Include only selected Categories & Tags feature added

= 2.0.7 - 2025-02-23 =
* Pro Version: Video Sitemap > X (Twitter) video support added
* WordPress 6.7.2 compatible

= 2.0.6 - 2025-02-10 =
* **sgg_sitemap_colors** filter added for customizing Sitemap Colors
* Pro Version: Sitemap Colors feature added with Color Pickers
* Pro Version: Toggle Branding Marks feature added

= 2.0.5 - 2025-01-07 =
* Additional URLs: Add Bulk URLs feature added

= 2.0.4 - 2024-12-07 =
* get_category_link() replaced with get_term_link() for Taxonomy URLs
* Compatibility with Custom Permalinks plugins improved

= 2.0.3 - 2024-11-22 =
* Archive Sitemap wp_ table prefix issue fixed. Thanks to @jdorner
* **xml_sitemaps_site_url** filter added for customizing Site URL
* WordPress 6.7.1 compatible

= 2.0.2 - 2024-11-13 =
* WordPress 6.7 compatible

= 2.0.1 - 2024-11-04 =
* Sitemap Index: Showing Single Sitemap issue fixed. Thanks to @waldub
* Image Sitemap: Duplicating Image Sizes removed

= 2.0.0 - 2024-10-14 =
* Setup Wizard added

= 1.9.12 - 2024-09-26 =
* Multiple Sitemap queries improved

= 1.9.11 - 2024-09-25 =
* WPML Category Sitemap issue fixed. Thanks to @waldub
* WPML compatibility improved

= 1.9.10 - 2024-09-12 =
* WordPress 6.6.2 compatible
* Media Sitemap: Outputting shortcodes issue fixed

= 1.9.9 - 2024-09-03 =
* Pro Version: Video Sitemap > Vimeo video support added
* WPML language names as parameter issue fixed

= 1.9.8 - 2024-07-29 =
* WordPress 6.6.1 compatible
* Exclude Categories and Tags query optimized

= 1.9.7 - 2024-07-18 =
* **pre_get_posts** hook replaced with **template_redirect** action

= 1.9.6 - 2024-07-16 =
* WordPress 6.6 compatible
* **sitemap** query var renamed to **inner_sitemap**

= 1.9.5 - 2024-07-09 =
* Paginated Sitemap with Multilingual plugins issue fixed
* Polylang displaying Not Translatable Post Types issue fixed. Thanks to Adrien Tay Pamart

= 1.9.4 - 2024-06-24 =
* Adding Custom Sitemaps for Sitemap Index feature added
* Static Sitemap File detector added
* **sgg_additional_index_sitemaps** filter data structure changed

= 1.9.3 - 2024-06-18 =
* Links Per Page issue with Plain Permalinks fixed

= 1.9.2 - 2024-06-17 =
* Sitemap Index: Separate Links Per Page feature added
* Links Per Page settings added
* Default Sitemap Structure changed to Sitemap Index
* Include Custom Post Types issue fixed
* WordPress 6.5.4 compatible

= 1.9.1 - 2024-06-04 =
* Excluding Archives from Sitemap issue fixed
* Excluding Pages from Sitemap issue fixed

= 1.9.0 - 2024-05-29 =
* Multilingual Sitemap Index with all language Sitemaps aadded
* Multilingual Sitemaps duplicating URLs issues fixed
* Multilingual Sitemaps caching issue fixed
* Authors Sitemap displaying none-author Users issue fixed

= 1.8.14 - 2024-05-22 =
* Empty Additional URLs warning fixed

= 1.8.13 - 2024-05-22 =
* Sitemap Index Queries optimized
* Conflict with YOAST SEO Sitemaps fixed

= 1.8.12 - 2024-05-16 =
* **sgg_additional_index_sitemaps** filter implemented for adding Custom Sitemaps to Sitemap Index

= 1.8.11 - 2024-05-08 =
* WordPress 6.5.3 compatible

= 1.8.10 - 2024-04-23 =
* Webserver Configuration section added for NGINX servers

= 1.8.9 - 2024-04-11 =
* WordPress 6.5.2 compatible
* Child Posts incorrect URL issue fixed

= 1.8.8 - 2024-04-04 =
* XML Sitemap: Enable/Disable Sitemap feature added
* Taxonomy, Archive, and Author queries optimized

= 1.8.7 - 2024-03-28 =
* Google News: Include Older Posts feature added
* Google News: Posts older than 48 hours excluded from Sitemap by default
* XML Sitemaps, Google News, Image Sitemap, Video Sitemap: Queries optimized
* Meta Box styles conflict fixed

= 1.8.6 - 2024-03-19 =
* WordPress 6.5 compatible
* Plugin Preview enabled
* Language files updated

= 1.8.5 - 2024-03-15 =
* IndexNow Check API Key feature added
* IndexNow Change API Key feature added
* Rank Math SEO noindex Pages, Posts excluded from Sitemaps
* Rank Math SEO noindex Categories, Taxonomies excluded from Sitemaps
* **sgg_sitemap_exclude_single_term** filter added for excluding single Term from Sitemap
* Deprecated Ping Search Engines feature removed
* Deprecated Ping Google News feature removed
* Deprecated Ping Google News when Post Status changed option removed

= 1.8.4 - 2024-03-04 =
* Added Settings **Patch** for Custom Post Type and Taxonomies
* PHP 8.2 deprecated creating Dynamic Properties error fixed
* Media Sitemap Settings dependency logic improved

= 1.8.3 - 2024-02-23 =
* FooGallery compatibility added
* **sgg_indexnow_api_key** filter added for customizing IndexNow API Key

= 1.8.2 - 2024-02-21 =
* IndexNow Protocol added for indexing Microsoft Bing, Seznam.cz, Naver, and Yandex
* IndexNow automatically ping feature added when Post published and saved

= 1.8.1 - 2024-02-14 =
* Generating XML Sitemap issue fixed

= 1.8.0 - 2024-02-14 =
* Added new features:
-- Import Settings
-- Export Settings
* **sgg_disable_xml_sitemap** filter added for disabling XML Sitemap
* Add Post Meta Box applied only for Public Post Types
* Gutenberg Plugin Sidebar error fixed

= 1.7.11 - 2024-02-08 =
* Documentation links added

= 1.7.10 - 2024-01-26 =
* Last Modified Datetime field added to Additional URLs
* **xml_sitemap_news_language** filter added for customizing Google News Language

= 1.7.9 - 2024-01-18 =
* Image Sitemap: Skipping URLs with query parameters issue fixed
* PHP 8.2 compatibility improved

= 1.7.8 - 2024-01-12 =
* **xml_sitemap_include_post** filter added for customizing Post inclusion in Sitemap
* Pro Version: Page/Post Custom Sitemap Options feature added:
-- Exclude from Sitemap
-- Post Priority
-- Post Frequency

= 1.7.7 - 2024-01-05 =
* Readme updated

= 1.7.6 - 2024-01-03 =
* UI/UX improvements

= 1.7.5 - 2023-12-22 =
* **sitemap_xsl_template_path** filter added for customizing XSL Template path

= 1.7.4 - 2023-12-15 =
* Sitemap Index URLs issue with Plain Permalinks fixed
* Choose Sitemap Structure settings improved
* WordPress 6.4.2 compatible

= 1.7.3 - 2023-12-04 =
* All `pre_get_posts` filters removed from Sitemap Queries
* Inner Sitemaps displaying issue with other Sitemap plugins fixed

= 1.7.2 - 2023-11-15 =
* Include Pages option added to Google News Settings
* Page, Post, Term Priority and Frequency filters added, thanks to @mosterojei

= 1.7.1 - 2023-11-10 =
* WordPress 6.4.1 compatible
* Taxonomy Settings removed from Google News
* Pro Version: Exclude Categories and Tags feature added. Thanks to @vladynol

= 1.7.0 - 2023-11-07 =
* Image Sitemap added to Sitemap Index
* Video Sitemap added to Sitemap Index
* Adding Google News to robots.txt feature added
* Adding Image and Video Sitemaps to robots.txt feature added

= 1.6.7 - 2023-11-06 =
* WordPress 6.4 compatible
* Settings issue for WordPress versions older than 5.5.0 fixed. Thanks to @stephanie0000

= 1.6.6 - 2023-10-30 =
* Minor bug fixes

= 1.6.5 - 2023-10-06 =
* Sitemap issue with Plain Permalinks fixed
* SimpleXMLElement replaced with DOMDocument for better performance

= 1.6.4 - 2023-10-04 =
* Multilingual Sitemap URLs for WPML, Polylang, and TranslatePress outputted in Settings
* TranslatePress removing XSL Template issue fixed. Thanks to @sunlight1976

= 1.6.3 - 2023-09-28 =
* Multiple Sitemaps by Sitemap Index feature moved from Pro to Free version
* PolyLang secondary languages issue fixed
* TranslatePress compatibility improved

= 1.6.2 - 2023-09-19 =
* get_home_url() changed to get_site_url() for XSL templates. Thanks to @bretzsofas
* Disabled auto-generated WP Sitemaps

= 1.6.1 - 2023-09-11 =
* Readme updated

= 1.6.0 - 2023-09-01 =
* WordPress 6.3.1 compatible
* Pro Version: Split Sitemaps into multiple Sitemap by Sitemap Index feature added
* Pro Version: Image Sitemap - Exclude Broken Images feature added

= 1.5.6 - 2023-08-16 =
* Admin Notices added

= 1.5.5 - 2023-08-07 =
* WordPress 6.3 compatible
* Pro Version: HTML Sitemap Widget - Show Featured Image, Show Excerpt, Show Date, Date Format options added

= 1.5.4 - 2023-08-01 =
* Video Sitemap: Player_loc URL escaping issue fixed. Thanks to @track77

= 1.5.3 - 2023-07-20 =
* Conflict with older Pro Version fixed

= 1.5.2 - 2023-07-13 =
* Google News: <news:news> metadata is removed for posts older than 48 hours. Thanks to @dima2duo
* Video Sitemap: Video Link combined with Video Title for better view
* Video Sitemap: Description length is limited to 300 characters for better view

= 1.5.1 - 2023-07-11 =
* `sgg_sitemap_post_media_urls` filter added to modify Media Sitemap URLs
* Advanced Settings features improved

= 1.5.0 - 2023-07-04 =
* Dashboard Sitemap Settings escaping issues fixed
* Google News > Post Title escaping issues fixed
* Sitemap > Last Modified Date output format changed to ISO 8601
* Google News > Publication Date output format changed to ISO 8601

= 1.4.9 - 2023-06-20 =
* Plugin deactivation link removed when Pro Version is activated
* Toolbar actions improved

= 1.4.8 - 2023-06-09 =
* Yoast SEO noindex Categories, Taxonomies excluded from Sitemaps

= 1.4.7 - 2023-06-02 =
* Yoast SEO noindex Pages, Posts excluded from Sitemaps
* Video & Image Sitemap styles improved
* Pro Version: YouTube checking API Key option added
* Pro Version: YouTube API Data Caching feature added

= 1.4.6 - 2023-05-27 =
* Sitemaps styles improved
* Older PHP versions (7.3) support improved

= 1.4.5 - 2023-05-15 =
* Sitemaps restyled
* Image Sitemap: Image Preview column added

= 1.4.4 - 2023-05-07 =
* Plugin name changed

= 1.4.3 - 2023-05-03 =
* Cache Sitemaps feature added
* Caching Timeout option added
* Last Cached Time information block added
* Clear Cache added
* Google News date format changed
* Pro Version: Smart Caching feature added

= 1.4.2 - 2023-04-23 =
* Pro Version: Featured Images option added
* Pro Version: WooCommerce Gallery feature added

= 1.4.1 - 2023-04-22 =
* MediaSitemap syntax error fixed

= 1.4.0 - 2023-04-18 =
* Image Sitemap feature added
* Video Sitemap feature added
* Sitemap Content Options improved

= 1.3.5 - 2023-04-01 =
* HTML Sitemap shortcode improved

= 1.3.4 - 2023-03-26 =
* Security Update

= 1.3.3 - 2023-03-22 =
* WordPress 6.2 compatible

= 1.3.2 - 2023-03-08 =
* Scheduled posts removed from Sitemaps
* Additional URLs saving issue fixed

= 1.3.1 - 2023-03-06 =
* Save Changes issue fixed
* Ping Google News issue fixed

= 1.3.0 - 2023-03-03 =
* Help-bar with Tools, Previews, Tips, and Links added
* Automatically ping Google News feature added
* Ping Google News when Post Status changed feature added
* User-Friendly Interface improvements

= 1.2.8 - 2023-02-25 =
* Plugin name changed

= 1.2.7 - 2023-02-22 =
* Pro Features added

= 1.2.6 - 2023-02-18 =
* Minor bug fixes

= 1.2.5 - 2023-02-10 =
* Google News excluding Posts issue fixed

= 1.2.4 - 2023-02-06 =
* Pro Features added

= 1.2.3 - 2023-01-20 =
* Polylang displaying Home Page URL issue fixed

= 1.2.2 - 2022-12-29 =
* Minor bug fixes

= 1.2.1 - 2022-11-27 =
* Google News Sitemap added
* Google News custom Publication name feature added
* Google News custom Sitemap URL feature added

= 1.2.0 - 2022-11-03 =
* Pro Version added

= 1.1.2 - 2022-10-30 =
* WordPress 6.1 compatible

= 1.1.1 - 2022-09-17 =
* Minimum PHP Version downgraded

= 1.1.0 - 2022-07-29 =
* Front page last modified time issue fixed

= 1.0.7 - 2022-05-13 =
* WordPress 6.0 compatible

= 1.0.6 - 2022-02-07 =
* WordPress 5.9 compatible

= 1.0.5 - 2021-11-21 =
* WordPress compatibility update

= 1.0.4 - 2021-10-03 =
* Bug fixes.

= 1.0.3 - 2021-07-08 =
* WordPress 5.8 compatible

= 1.0.2 - 2021-03-13 =
* WordPress 5.7 compatible

= 1.0.1 - 2021-02-21 =
* Bug fixes

= 1.0.0 - 2021-02-17 =
* Release