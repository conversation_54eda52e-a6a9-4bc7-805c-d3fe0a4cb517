@import "../assets/fonts";

*{
	box-sizing: border-box;
}

html{
	height: 100%;
	height: 100dvh;
}

html[dir="rtl"] body {
	// direction: rtl;
    // text-align: right;
}

body{
	font-family: "<PERSON><PERSON>", "<PERSON><PERSON>", "Helvetica Neue", "Tahoma", sans-serif;
	color: #0c0f17;
	height: 100%;
	overflow-x: hidden;
	margin: 0;
}

a, a:hover{
	text-decoration: none;
	color: #007afd;
}

ul, ol{
	margin: 0;
	padding: 0;
	list-style: none;
}

img{
	max-width: 100%;
	height: auto;
}

main{
	display: flex;
	flex-direction: column;
	min-height: 100%;
	//overflow-x: hidden;

	.wrap-content{
		flex-grow: 1;
	}
}

.container{
	width: 100%;
	max-width: 1536px;
	margin: 0 auto;
	padding-right: $desktop_horizontal_spacing;
	padding-left: $desktop_horizontal_spacing;

	@include mq($until: 1200){
		max-width: 1140px;
	}

	@include mq($until: 992){
		max-width: 960px;
	}

	@include mq($until: 768){
		max-width: 720px;
	}

	@include mq($until: 576){
		max-width: 540px;
	}
}