header{
	font-size: 14px;
	height: 60px;

	@include mq($until: 1350){
		@include from-tablet(){
			&:not(.has-second-nav){
				height: 117px;
			}
		}
	}

	@include mobile(){
		height: 60px;
	}

	.admin-bar &{
		@include mq($until: 600){
			position: sticky;
			top: 0px;
			z-index: 10;

			.main-navigation, .mobile-navigation{
				position: static !important;
			}
		}
	}

	.main-navigation{
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		padding-right: 20px;
		padding-left: 20px;
		border-bottom: 1px solid #DFE2EB;
		background-color: #fff;
		z-index: 9;
		transform: translate3d(0, 0, 0);
		max-height: 60px;
		

		@include tablet(){
			display: none !important;
		}

		@include mobile(){
			padding-right: $mobile_horizontal_spacing;
			padding-left: $mobile_horizontal_spacing;
		}

		.admin-bar &{
			top: 32px;

			@include mq($until: 782){
				top: 46px;
			}
		}

		.column-info{
			display: flex;
			white-space: nowrap;
			justify-content: flex-end;
			flex-grow: 1;
		}

		&.has-gutenberg-nav{
			.column-info{
				.phone{
					display: none;
				}
			}
		}

		&:not(.has-second-nav){
			.wrap-nav{
				flex-wrap: nowrap;
			}
		}

		@include mq($until: 1350){
			@include from-tablet(){
				&:not(.has-second-nav){
					.wrap-nav{
						flex-wrap: wrap;
					}

					.column-menu{
						flex-grow: 1;
						width: calc(100% - 200px);

						> ul{
							justify-content: flex-end;
						}
					}

					.column-info{
						position: relative;
						flex-basis: 100%;
						padding: 5px 20px;
						max-height: 60px;
						margin-left: -20px;
						margin-right: -20px;
						//border-top: 1px solid #dfe2eb;
						border-bottom: 1px solid #dfe2eb;
						background-color: #f5f6fa;

						&:before{
							content: '';
							position: absolute;
							top: 0;
							left: 0;
							width: calc(100% + #{$desktop_horizontal_spacing} * 2);
							margin: 0 (-1 * $desktop_horizontal_spacing);
							border-top: 0px solid #dee1ea;
						}
					}
				}
			}
		}
	}

	.wrap-nav{
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		min-height: 60px;
		padding: 0;
		@include mobile(){
			flex-wrap: nowrap;
		}

		ul{
			margin: 0;
			padding: 0;
			list-style: none !important;

			li{
				margin: 0;
				padding: 0;
			}
		}

		&:not(.full-width-container-has-auto-more){
			.column-menu{
				display: flex;
				overflow-x: hidden;
			}
		}

		.column-menu{
			flex-grow: 1;
		}
	}

	&.has-second-nav{
		.wrap-nav{
			flex-wrap: nowrap;
		}
	}

	.header_menu{
		display: flex;
		white-space: nowrap;
		flex-wrap: nowrap;
		min-height: 60px;

		> li{
			@include navigation-item-li();
			@include navigation-children-ul();
			&.auto-more-button{
				position: static!important;
			}

			&.has-child{
				@include navigation-li-has-child("&:after");
			}

			> a{
				padding-top: 14px;
				padding-bottom: 14px;
			}

			ul{
				li{
					@include navigation-children-ul-li();
					min-height: 30px;
				}
			}
		}
	}

	.column-logo{
		padding-right: 12px;
		a{
			display: block;
			
		}
		img{
			max-width: 184px;
		}
	}

	// mobile navigation
	.mobile-navigation{
		@extend .main-navigation;

		@include tablet(){
			display: block !important;
		}

		&.has-gutenberg-nav{
			.column-info{
				.phone{
					display: none;
				}
			}

			.column-open-second-menu{
				display: block;
			}
		}

		.wrap-nav{
			height: 60px;

			@include mobile(){
				height: 60px;
			}

			.column-info{
				.phone{
					@include mq($until: 1050){
						display: flex !important;
					}
					@include mq($until: 500){
						display: none !important;
					}
				}
			}
		}

		.column-open-second-menu{
			display: none;
			flex-grow: 1;

			.second-menu-open{
				display: none;

				&.opened{
					transform: rotate(180deg);
				}

				@include mobile(){
					display: block;
				}
			}
		}

		.column-info{
			.phone{
				@include mq($until: 1050){
					display: flex !important;
				}
			}
		}

		.column-menu-open{
			.burger-menu-open{
				.icon{
					display: block;
					width: 24px;
					height: 24px;
					margin-right: 0;
					// background-image: url(./image/menu.svg);
					// background-repeat: no-repeat;
					// background-size: cover;
					// @extend %navigation-info-icon;
				}
			}
		}
	}
}


.auto-more-button a{
	z-index: 1;
	position: relative;
}