.language-switcher{
	position: relative;
	line-height: 1.5;
	padding: 0!important;
	outline: none;
	text-transform: capitalize;


	.enabled{
		display: flex;
		gap: 5px;
		padding: 12px;
		border: 1px solid transparent;
		border-bottom: 0;
		border-radius: 2px 2px 0 0;
		transition: all, .3s;
		position: relative;
		z-index: 1;

		span{
			cursor: default;
		}

		.icon{
			display: flex;
			align-items: center;
			justify-content: center;
			&:after{
				content: "";
				position: relative;
				top: -2px;
				display: block;
				width: 8px;
				height: 8px;
				margin: 0 6px;
				transition: color .25s ease;
				z-index: 99;
				transform: rotate(225deg);
				border-top: 2px solid #0054cc;
				border-left: 2px solid #0054cc;
			}
			&:hover:after{
				transform: rotate(45deg);
				top: 2px;
			}
		}
	}

	.list{
		position: absolute;
		min-width: 100%;
		margin-top: -45px;
		left: -2px;
		opacity: 0;
		visibility: hidden;
		transition: visibility .3s, opacity .3s;

		> div{
			display: block;
			padding: 0;
			border: none;
		}

		> ul, > div > ul{
			padding: 36px 20px 10px 14px;
			border: 1px solid rgb(212, 213, 217);
			border-radius: 7px;
			background-color: rgb(255, 255, 255);

			li{
				display: block;
				padding-top: 5px;
				padding-bottom: 5px;

				a{
					display: flex;
					line-height: inherit;
					align-items: center;
					padding: 0;
				}
			}
		}
	}

	.enabled{
		.wrap-text, .icon{
			position: relative;
			z-index: 1;
		}
	}

	a, .enabled > span{
		display: block;
		font-weight: 700;
		line-height: inherit;
		text-decoration: none;
		color: inherit;
		padding: 0;

		img{
			+ span{
				margin-left: .4em;
			}
		}
	}



	&:hover{
		.enabled{
			.icon:after{
				transform: rotate(45deg);
				top: 2px;
			}
		}

		.list{
			opacity: 1;
			visibility: visible;
			a:hover{
				color: #0055cc;
			}
		}
	}
}