body.main-menu-opened{
	overflow: hidden;
}

#mobile-menu, #second-mobile-menu{
	position: absolute;
	width: 100vw;
	height: 100vh;
	background: #f5f6fa;

	li{
		display: block !important;

		&.auto-more-button{
			display: none !important;
		}
	}
}

#mobile-menu{
	position: fixed;
	top: 0;
	right: 0;
	display: block;
	width: 0;
	height: 100%;
	transition: width 0.4s cubic-bezier(0.25, 0.8, 0.25, 1);
	z-index: 100000;

	&.opened{
		width: 100%;
	}

	.inner-mobile-menu{
		position: absolute;
		display: flex;
		font-size: 16px;
		flex-direction: column;
		width: 100%;
		min-width: 100vw;
		//height: calc(100% - 20px);
		height: 100%;
		background: #f5f6fa;
		overflow-y: auto;
	}

	.main-menu-header{
		display: flex;
		text-align: center;
		align-items: center;
		justify-content: space-between;
		flex-direction: row;
		width: 100%;
		padding: 20px 12px;
		border-bottom: 1px solid rgb(222, 225, 234);
		background: #fff;
		max-height: 60px;

		.menu-close{
			// width: 17px;
			// height: 17px;
			background-color: transparent;
			border: none;
			padding: 0;
			display: block;
			// background: url(../../../assets/images/icons/close.svg);
			// background-repeat: no-repeat;
			// background-size: contain;
		}

		.column-logo{
			padding-right: 8px;


			img {
			
				max-width: 184px;

			}
		}
	}

	.wrap-nav{
		overflow: auto;
		display: flex;
		align-items: stretch;
		flex-direction: column;
		padding-right: $desktop_horizontal_spacing/2;
		padding-left: $desktop_horizontal_spacing/2;

		.column-logo{
			display: none;
		}

		.column-menu{
			flex-grow: 0;
		}

		.column-info{
			display: block;

			> *{
				padding: 16px 0;
				border-top: 1px solid #dee1ea;
			}

			> .login{
				padding: 0;
				margin-right: 0;

				> .login-link{
					padding: 16px 0;
					font-weight: 500;
				}
			}
		}

		.header_menu{
			display: block;
			position: relative;
			z-index: 0;

			li{
				@include navigation-item-li();
				padding: 0;

				a{
					padding: 12px 0;
					font-weight: 500 !important;
					font-size: 16px;
				}

				+ li{
					border-top: 1px solid #dee1ea;
				}
			}
		}
	}

	.main-menu-footer{
		display: flex;
		border-top: 1px solid #dee1ea;
		margin: 0 12px;
		.language-switcher{
			

			.enabled{
				position: relative;
				padding: 20px 12px;
				border: 0;
				background-color: transparent;
				z-index: 2;
			}

			.list{
				margin-top: -4px;
    			transform: translateY(-100%);
				ul{
					padding-top: 10px;
					padding-bottom: 44px;
				}
			}

			a, span{
				font-weight: 500;
			}

			&:hover{
				.enabled{
					&:before{
						transform: scale(1) rotate(90deg);
					}
				}
			}
		}
	}

}

