header{
	position: relative;
	font-size: 13px;
	height: $main-nav-height;
	z-index: 300;

	@include tablet(){
		height: $main-nav-tablet-height;
	}

	@include mobile(){
		height: $main-nav-mobile-height;
	}

	.admin-bar &{
		@include mq($until: 600){
			position: sticky;
			top: 0px;
			z-index: 9999;

			.main-navigation, .mobile-navigation{
				position: static !important;
			}
		}
	}

	.fixed-header{
		position: fixed;
		top: 0;
		left: 0;
		display: flex;
		align-items: center;
		width: 100%;
		height: $main-nav-height;
		border-bottom: 1px solid #D5DCE0;
		background: #fff;
		z-index: 9;
		transform: translate3d(0, 0, 0);

		@include adminBarIndent() using($value){
			top: $value + px;
		}

		@include tablet(){
			height: $main-nav-tablet-height;
		}

		@include mobile(){
			height: $main-nav-mobile-height;
		}

		@include small_mobile(){
			.admin-bar &{
				position: absolute;
				top: 0px !important;
			}
		}
	}

	//&.main-header-has-theme-blue-bg, &.main-header-has-black-bg{
	//	.fixed-header{
	//		background: #0055CC;
	//	}
	//}

	.main-navigation{
		width: 100%;

		
		.column-info{
			display: flex;
			white-space: nowrap;
			height: $main-nav-item-height;

			.language-switcher{
				display: flex;
				align-items: center;
				flex-direction: column;
				height: 100%;

				// &:after{
				// 	content: '';
				// 	display: block;
				// 	align-self: center;
				// 	width: 0;
				// 	height: 1px;
				// 	background-color: #0055CC;
				// 	transition: width .3s;
				// }

				&:hover{
					&:after{
						width: calc(100% + 15px * 2);
					}
				}

				.enabled{
					flex-grow: 1;
				}

				.list{
					top: 100%;
					left: 0;
					margin-top: $main-nav-height/2 - $main-nav-item-height/2;

					&:before{
						top: $main-nav-item-height/2 - $main-nav-height/2;
					}
				}
			}
		}

		&.has-gutenberg-nav{
			.column-info{
				.phone{
					display: none;
				}
			}
		}

		.wrap-nav{
			display: flex;
			align-items: center;

			@include mobile(){
				flex-wrap: nowrap;
			}

			ul{
				margin: 0;
				padding: 0;
				list-style: none !important;

				li{
					margin: 0;
					padding: 0;

					&.menu-item-has-children:not(.has-menu-contact-info), &.has-child{
						@include navigation-li-has-child('> a:after');

						> a{
							padding-right: 33px;

							@include mq($until: 1200){
								padding-right: 23px;
							}

							&:after{
								content: '';
								position: absolute;
								top: 50%;
								right: 15px;
								display: block;
								margin-top: -4.5px;

								@include mq($until: 1200){
									right: 6px;
								}
							}
						}
					}

					a{
						position: relative;
					}
				}
			}

			.header_menu{
				display: flex;
				justify-content: center;

				> li{
					@include navigation-item-li();
					@include navigation-children-ul();

					> a{
						display: flex;
						white-space: nowrap;
						align-items: center;
						justify-content: center;
						height: $main-nav-item-height;

						/*@include tablet(){
							height: $main-nav-tablet-height;
						}

						@include mobile(){
							height: $main-nav-mobile-height;
						}*/
					}

					&:not(.has-menu-contact-info):not(.auto-more-button){
						ul{
							top: $main-nav-height;
							left: 50%;
							align-items: flex-start;
							justify-content: space-between;
							flex-direction: row;
							flex-wrap: wrap;
							width: $main-nav-sub-menu-width;
							margin-left: -($main-nav-sub-menu-width / 2);
							padding: 35px 40px;

							@include mq($until: $main-nav-sub-menu-width){
								left: 0;
								margin-left: 0;
							}

							li{
								@include navigation-children-ul-li();
								width: 33.3333%;
								margin: 15px 0;
								padding: 0 10px;

								a{
									font-size: 24px;
									font-weight: 300;
									line-height: 140%;
									padding: 0;
								}

								.menu-item-description{
									color: #BDBDBD;
								}
							}
						}
					}

					&.children-template-two-columns-icons.children-template-two-columns-icons{
						> ul{
							> li{
								@include navigation-children-ul-li('> .item-wrap-link > a, > a');
								display: flex;
								align-items: flex-start;
								width: 50%;
								margin: 15px 0;
								padding: 0 15px;

								> .item-wrap-link > a, > a{
									margin-bottom: 10px;
								}

								.item-icon{
									position: relative;
									display: flex;
									align-items: center;
									justify-content: center;
									min-width: 72px;
									min-height: 72px;
									margin-right: 30px;

									svg{
										max-width: 32px;
										max-height: 32px;
									}

									.item-icon-bg{
										position: absolute;
										top: 0;
										left: 0;
										width: 100%;
										height: 100%;
										border-radius: 50%;
										background: #E5EEFA;
										opacity: .105;
									}
								}
							}
						}
					}

					&.has-menu-contact-info{
						> ul{
							top: $main-nav-height;
							width: 341px;

							.popup-contact-info{
								font-size: 14px;

								> div{
									+ div{
										margin-top: 30px;
									}

									// > *{
									// 	&.title{
									// 		+ *{
									// 			margin-top: 0;
									// 		}
									// 	}

									// 	+ *{
									// 		margin-top: 10px;
									// 	}
									// }
								}

								.title{
									font-size: 24px;
									font-weight: 300;
									line-height: 110%;
									color: #0055CC;
									margin-bottom: 12px;
								}

								.phone, .working-time{
									display: flex;

									&:before{
										content: '';
										display: block;
										width: 20px;
										height: 20px;
										margin-right: 10px;
										background-size: cover;
									}
								}

								// .phone{
								// 	&:before{
								// 		background-image: url(../../../assets/images/icons/phone-gray.svg);
								// 	}
								// }

								// .working-time{
								// 	&:before{
								// 		background-image: url(../../../assets/images/icons/clock-gray.svg);
								// 	}
								// }

								.button-link{
									width: 100%;
								}
							}

							&.sub-menu-align-left{
								right: 0;
							}
						}
					}

					&.auto-more-button{
						> ul{
							top: $main-nav-height;
							font-size: 24px;
							padding-right: 0;
							padding-left: 0;

							> li{
								@include navigation-children-ul-li('> a');
								@include navigation-children-ul('ul');
								padding-right: 50px !important;
								padding-left: 50px !important;

								&.menu-item-has-children{
									> a{
										padding-right: 35px;
									}
								}

								ul{
									left: 100%;
									width: max-content;
									max-width: none;
									margin-top: -1.4em;
									margin-left: 3px;

									&.sub-menu-align-left{
										right: 100%;
										left: auto;
										margin-right: 3px;
										margin-left: 0;
									}

									> li{
										@include navigation-children-ul-li('> a');
									}

									&:before, &:after{
										content: '';
										position: absolute;
										top: 0;
										left: -3px;
										width: 3px;
										height: 100%;
										margin: 0;
									}

									&:after{
										right: -3px;
										left: auto;
									}
								}

								a{
									display: flex;
									font-weight: 300;
									align-content: center;
									justify-content: space-between;
									padding: 0;

									&:after{
										top: auto;
										right: 0;
										width: 16px;
										height: 16px;
										margin: 0;
										transform: rotate(-90deg);
									}
								}
							}

							li{
								position: static;
								padding: 10px 0;
							}

							&.sub-menu-align-left{
								right: 0;
							}
						}
					}
				}
			}

			&:not(.full-width-container-has-auto-more){
				.wrap-header-menu-ul{
					margin-bottom: -16px;
				}
			}

		}
	}

	.column-menu{
		flex-grow: 1;
		overflow: hidden;
	}

	.column-menu-open{
		display: none;

		.burger-menu-open{
			.icon{
				display: block;
				width: 24px;
				height: 24px;
				margin-right: 0;
				// background-image: url(./image/menu.svg);
				// background-repeat: no-repeat;
				// background-size: cover;
				// @extend %navigation-info-icon;
			}
		}
	}

	.column-logo{
		padding-right: 8px;

		img{
			max-width: 184px;
		}

		// svg{
		// 	max-width: 155px;
		// 	height: 40px;
		// 	max-height: 40px;

		// 	path{
		// 	}
		// }
	}

	/* mobile nav */
	@include mq($until: 980){
		.main-navigation{
			.column-menu{
				display: none;
			}

			.column-info{
				.phone, .language-switcher{
					display: none !important;
				}

				.login{
					padding: 0;

					.icon{
						margin: 0;
					}

					.login-link span{
						display: none;
					}
				}
			}

			.wrap-nav{
				justify-content: space-between;
			}

			.column-logo{
				svg{
					max-width: 124px;
					height: 32px;
					max-height: 32px;
				}
			}
		}

		.column-menu-open{
			display: block;
		}
	}
}

