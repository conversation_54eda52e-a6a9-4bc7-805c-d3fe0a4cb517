@import "widgets";

footer{
	color: #000;
	padding-top: (63px - 20);
	background-color: #f4f6f9;

	@include tablet(){
		padding-top: (50px - 20);
	}

	@include small_mobile(){
		padding-top: 0;
	}


	.widget-title.widget-title.widget-title{
		margin-top: 0;
		margin-bottom: 0;
	}

	.widget-inner-container{
		margin-right: auto;
		margin-left: auto;
	}

	.footer-middle{
		padding: 20px 0;
		border: 1px solid #dee1ea;
		border-right: 0;
		border-left: 0;

		.contact-info{
			display: flex;
			align-items: center;
			justify-content: space-between;

			@include tablet(){
				flex-wrap: wrap;
			}

			> *{
				display: flex;
				justify-content: flex-start;
				flex-basis: 100%;

				+ *{
					@include tablet(){
						margin-top: 16px;
					}
				}
			}

			.phone, .email > a{
				display: flex;
				gap: 5px;

				.icon{
					&:after{
						content: '';
						display: block;
						width: 24px;
						height: 24px;
						background-repeat: no-repeat;
						background-position: center;
						background-size: contain;
					}
				}
			}

			.phone{
				font-weight: bolder;
			}
		}
	}

	.footer-bottom{
		padding-bottom: 22px;

		.language-switcher .list{
			bottom: 0;

			& > div > ul{
				padding: 10px 20px 40px 14px;
			}
		}

		.footer-bottom-widgets-wrap{
			display: flex;
			justify-content: space-between;
			max-width: 100%;
			margin: 0 (-$desktop_horizontal_spacing/2);

			> div{
				padding: 5px $desktop_horizontal_spacing/2;
			}

			@include mobile(){
				flex-wrap: wrap;
			}
		}
	}
}