footer{
	> .container{
		@include small_mobile(){
			padding-right: 10px;
			padding-left: 10px;
		}
	}

	.footer-top{
		display: flex;
		justify-content: flex-start;
		flex-wrap: wrap;
		margin-right: -15px;
		margin-left: -15px;
		padding-top: 10px;

		@include tablet(){
			margin-right: -10px;
			margin-left: -10px;
		}

		@include mobile(){
			margin-right: -5px;
			margin-left: -5px;
		}

		@include mq($until: 600){
			display: block;
		}

		.widgets-wrap{
			display: flex;
			flex-basis: calc(100% - 30% - 15px * 2);
			flex-grow: 1;

			&:not(.widgets-count-4){
				flex-wrap: wrap;
			}

			@include mobile(){
				flex-wrap: wrap;
			}
			@include mq($until: 600){
				flex-direction: column;
				.footer-card{
					margin: 10px 0;
				}
			}
		}

		.footer-card{
			flex-basis: 20%;
			flex-grow: 1;
			margin-top: 20px;
			margin-bottom: 20px;
			padding-right: 15px;
			padding-left: 15px;

			.widget-inner-container{
				min-width: 120px;
			}

			@include tablet(){
				flex-grow: 1;
				padding-right: 10px;
				padding-left: 10px;
			}
			@include mobile(){
				padding-right: 5px;
				padding-left: 5px;
				margin: 10px 0;
			}
		}

		.contacts-widget{
			/* 100%/1310*408 */
			flex-basis: calc(30% + 15px * 2);
			min-width: 316px;
			max-width: 408px + 15px * 2;

			@include mq($until: 1020){
				flex-basis: 100%;
				max-width: none;

				.widget-title{
					height: auto;
				}
			}
			@include mq($until: 350){
				min-width: auto;
			}

			.newsletter-subscribe{
				margin: 30px 0;

				@include mq($until: 1020){
					margin: 0;
				}
				@include mq($until: 669){
					margin: 20px 0;
				}
			}

			.theme-social-links{

				.amazon-image{
					svg{
						width: 92px;
					}
				}

				.list{
					ul{
						display: flex;
						@include mq($until: 460){
							justify-content: flex-end;
						}

						li{
							flex-basis: 25%;
							width: auto;

							@include mq($until: 1020){
								max-width: 54px;
							}
						}
					}
				}

				.social-icon{
					display: block;
				}
			}

			.content-container{
				@include mq($until: 1020){
					@include mq($from: 670){
						display: flex;
						justify-content: space-between;
					}
				}
			}

			.contacts{
				> div{
					margin-top: 15px;

					@include mq($until: 600){
						margin-top: 10px;
					}

					&:first-child{
						@include mq($until: 1020){
							margin-top: 20px;
						}
					}
				}

				&, a{
					font-size: 18px;
					line-height: 140%;
					color: #000;
				}
			}

			.networks-column{

			}
		}

		.widget-title.widget-title.widget-title{
			font-size: 18px;
		}

		.widget-title{
			position: relative;
			display: flex;
			font-weight: 700;
			color: inherit !important;
			height: 50px;
			margin: 0;

			@include mq($until: 600){
				&:not(.not-slide){
					align-items: center;
					height: auto;
					cursor: pointer;

					&:after{
						content: '';
						position: absolute;
						right: 10px;
						display: block;
						width: 8px;
						height: 8px;
						background-image: url(../../_mixins/navigation/image/drop.svg);
						background-position: center;
						transition: transform .3s;
					}

					&.open{
						&:after{
							transform: rotateX(180deg);
						}
					}
				}
			}
		}

		nav{

			ul{
				margin: 0;
				padding: 0;
			}

			li{
				display: block;
				margin-top: 20px;

				@include mq($until: 600){
					margin-top: 10px;
				}

				&:first-child{
					@include mq($until: 1020){
						margin-top: 20px;
					}
				}

				a{
					font-size: 16px;
					font-weight: 400;
					text-decoration: none;
					color: #000;

					// @include mq($until: 1200){
					// 	font-size: 13px;
					// }
				}
			}
		}
	}

	.footer-bottom{
		line-height: 150%;

		@include mobile(){
			flex-wrap: wrap;
		}

		.footer-card{
		}

		.widget_language_switcher, .widget_icl_lang_sel_widget{
			display: flex;
			font-size: 12px;
			align-items: center;

			.wpml-ls-legacy-list-horizontal{
				padding: 0;
				border: 0;
			}

			.switcher-list, ul{
				display: flex;
				flex-wrap: wrap;
				gap: 5px 20px;

				li{
					margin: 0;

					a{
						font-size: 12px;
						line-height: 150%;
						color: #000;
						margin: 0;
						padding: 0;
					}

					&.active, &.wpml-ls-current-language{
						span{
						}
					}
				}
			}
		}

		.widget_text, .text{
			font-size: 12px;
			color: #000;
			display: flex;
			align-items: center;
			@include mobile(){
				text-align: left;
				padding-right: 0;
				padding-left: 0;
			}
		}
	}
}