<?php
/**
 * Template Name: Post Data Viewer
 */
get_header();
?>


<?php
global $wp_filter;
var_dump($wp_filter['wp_insert_post']);

?>

<?php

//$posst = get_post(20348);

// $post_id = 20348; // Замените на ID нужного поста

// //var_dump($posst);

// // Получаем переводы для поста
// $translations = apply_filters( 'wpml_get_element_translations', null, $post_id, 'post' );

// if ( is_array( $translations ) && !empty( $translations ) ) {
//     foreach ( $translations as $lang => $translation ) {
//         echo "Язык: $lang, ID перевода: " . $translation->element_id . "<br>";
//     }
// } else {
//     echo "Переводы для поста с ID $post_id не найдены.";
// }



// $args = array(
//     'post_type'      => 'wp_navigation',
//     'posts_per_page' => -1, // Получаем все посты
//     'post_status'    => 'publish'
// );

// $query = new WP_Query( $args );
// echo 'Количество постов wp_navigation: ' . $query->found_posts;
// wp_reset_postdata();
?>



<?php get_footer(); ?>
