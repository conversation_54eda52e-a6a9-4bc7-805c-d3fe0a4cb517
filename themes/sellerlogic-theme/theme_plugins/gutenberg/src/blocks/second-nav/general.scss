.theme-block-second-nav{


	.icon{
		@extend %navigation-info-icon;
	}

	.column-info{
		display: flex;
		font-size: 14px;
		align-items: center;

		.phone{
			display: flex;
			white-space: nowrap;
			align-items: center;
			margin-right: 0;
			gap: 5px;
			.icon{
				@extend %navigation-info-icon-phone;
			}
		}
	}

	.wp-block-navigation__responsive-container-content{
		font-size: 14px;
		background-color: transparent !important;

		& > ul{
			background-color: transparent !important;
		}

		.wp-block-navigation-item__label{
			font-family: inherit;
			font-size: inherit;
		}

		.wp-block-navigation__container, .wp-block-navigation{
			> .wp-block-navigation-item{
				display: flex;
				color: #0c0f17;
				align-items: center;
				margin: 0 !important;

				.wp-block-navigation-item .wp-block-navigation-item__content{
					margin-right: 20px;
					padding: 0 4px 9px;
				}

				&.auto-more-button:hover, &.scroll-active, &:hover{
					& > .wp-block-navigation-item__content{
						color: #0055cc;
					}
				}

				.wp-block-navigation-item__content{
					display: block;
					font-weight: 700;
					text-decoration: none;
					color: #0c0f17;
					padding: 17px 12px;
					transition: color .25s ease;

					@include mq($until: 1200){
						padding-right: 10px;
						padding-left: 10px;
					}

					&:not([href]){
						cursor: default;
					}

					// > span{
					// 	position: relative;
					// 	z-index: 99;
					// }
					&.current-menu-item, &.current-menu-parent, &.scroll-active, &:hover{
						color: #05c;
					}
				}
			}
		}
	}
}



