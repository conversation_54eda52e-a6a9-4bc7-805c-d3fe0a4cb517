.before-footer-socials {
    display: flex;
    -webkit-align-items: center;
    align-items: center;
    justify-content: center;
    min-height: 201px;
    padding: 24px 10px;
    border-bottom: 1px solid #c8cddb;
    background: #f6f8f9;
	width: 100%;
}

.theme-block-social-links{
	max-width: 100%;
	padding: 0;
}

.social-prev-text{
	font-size: 24px;
    font-weight: bold;
    line-height: 28px;
    color: #000000;
    margin: 20px;
}

.theme-social-links{
	display: flex;
	flex-wrap: wrap;
	text-align: center;
    align-items: center;
    justify-content: space-around;
    width: 100%;
    max-width: 900px;

	&.layout-old-small{
		a{
			&[href*="twitter.com/"], &[href*="//x.com/"]{
				.social-icon{
					&:before{
						content: url(img/twitter.svg);
					}
				}
			}

			&[href*="xing.com/"]{
				.social-icon{
					&:before{
						content: url(img/xing.svg);
					}
				}
			}

			&[href*="linkedin.com/"]{
				.social-icon{
					&:before{
						content: url(img/linkedin.svg);
					}
				}
			}

			&[href*="facebook.com/"]{
				.social-icon{
					&:before{
						content: url(img/facebook.svg);
					}
				}
			}
		}
	}

	&.layout-big, &.layout-big-with-text, &.layout-footer, &.layout-large, &.layout-small{
		a{
			&[href*="twitter.com/"], &[href*="//x.com/"]{
				.social-icon{
					&:before{
						background-image: url(img/twitter.svg);
					}
				}
			}

			&[href*="xing.com/"]{
				.social-icon{
					&:before{
						background-image: url(img/xing.svg);
					}
				}
			}

			&[href*="linkedin.com/"]{
				.social-icon{
					&:before{
						background-image: url(img/linkedin.svg);
					}
				}
			}

			&[href*="facebook.com/"]{
				.social-icon{
					&:before{
						background-image: url(img/facebook.svg);
					}
				}
			}
		}
	}

	&.layout-big, &.layout-big-with-text, &.layout-footer, &.layout-large, &.layout-small{
		align-items: center;

		.amazon-image{
			margin-right: 10px;

			svg{
				width: 108px;
				height: 30px;

				path{
					//fill: #fff;
				}
			}
		}

		.list{
			flex-grow: 1;
		}

		ul{
			display: flex;
			flex-wrap: nowrap;
			justify-content: center;
			list-style: none;
			margin: 0;
   			padding: 0;
		}

		a{
			.social-icon{
				display: inline-block;
				width: 100%;
				height: auto;

				&:before{
					content: '';
					display: block;
					padding-bottom: 100%;
					border-radius: 50%;
					background-repeat: no-repeat;
					background-position: center;
					overflow: hidden;
				}
			}
		}
	}

	&.layout-small{
		a{
			.social-icon{
				&:before{
					background-size: 200%;
				}
			}
		}
	}

	&.layout-footer{
		flex-wrap: nowrap;
	}

	&.layout-footer, &.layout-big-with-text{
		ul{
			display: flex;
			flex-wrap: nowrap;

			li{
				line-height: 0;
				width: 25%;
				max-width: 64px;
				a{
					display: block;
				}
			}
		}
	}


	.social-icon{
		width: 24px;
		max-width: none;
		height: 24px;
	}

	.social-prev-text{
		margin-right: 15px;
	}

	.list{
		ul{
			display: flex;
			gap: 10px;
		}
	}
}