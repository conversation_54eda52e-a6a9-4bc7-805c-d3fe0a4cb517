.theme-social-links{
	display: flex;
	flex-wrap: wrap;
	gap: 15px;
	&.layout-old-small{
		a{
			&[href*="twitter.com/"], &[href*="//x.com/"]{
				.social-icon{
					&:before{
						content: url(../../icons/small/twitter.svg);
					}
				}
			}

			&[href*="xing.com/"]{
				.social-icon{
					&:before{
						content: url(../../icons/small/xing.svg);
					}
				}
			}

			&[href*="linkedin.com/"]{
				.social-icon{
					&:before{
						content: url(../../icons/small/linkedin.svg);
					}
				}
			}

			&[href*="facebook.com/"]{
				.social-icon{
					&:before{
						content: url(../../icons/small/facebook.svg);
					}
				}
			}
		}
	}

	&.layout-big, &.layout-big-with-text, &.layout-footer, &.layout-large, &.layout-small{
		a{
			&[href*="twitter.com/"], &[href*="//x.com/"]{
				.social-icon{
					&:before{
						background-image: url(../../icons/big/twitter.svg);
					}
				}
			}

			&[href*="xing.com/"]{
				.social-icon{
					&:before{
						background-image: url(../../icons/big/xing.svg);
					}
				}
			}

			&[href*="linkedin.com/"]{
				.social-icon{
					&:before{
						background-image: url(../../icons/big/linkedin.svg);
					}
				}
			}

			&[href*="facebook.com/"]{
				.social-icon{
					&:before{
						background-image: url(../../icons/big/facebook.svg);
					}
				}
			}
		}
	}

	&.layout-big, &.layout-big-with-text, &.layout-footer, &.layout-large, &.layout-small{
		align-items: center;

		.amazon-image{
			svg{
				width: 108px;
				height: 30px;

				path{
					//fill: #fff;
				}
			}
		}

		.list{
			flex-grow: 1;
		}

		ul{
			display: flex;
			flex-wrap: nowrap;
		}

		a{
			.social-icon{
				display: inline-block;
				width: 100%;
				height: auto;

				&:before{
					content: '';
					display: block;
					padding-bottom: 100%;
					border-radius: 50%;
					background-repeat: no-repeat;
					background-position: center;
					overflow: hidden;
				}
			}
		}
	}

	&.layout-small{
		a{
			.social-icon{
				&:before{
					background-size: 200%;
				}
			}
		}
	}

	&.layout-footer{
		flex-wrap: nowrap;
	}

	&.layout-footer, &.layout-big-with-text{
		ul{
			display: flex;
			flex-wrap: nowrap;

			li{
				line-height: 0;
				width: 25%;
				max-width: 64px;
			}
		}
	}

	&.layout-large{
		a{
			.social-icon{
				width: 48px;
				height: 48px;
			}
		}

		ul{
			gap: 10px;
			li{
				width: auto;
			}
		}
	}

	.social-icon{
		width: 24px;
		max-width: none;
		height: 24px;
	}

	.social-prev-text{
		margin-right: 15px;
	}

	.list{
		ul{
			display: flex;
			gap: 5px;
		}
	}
}